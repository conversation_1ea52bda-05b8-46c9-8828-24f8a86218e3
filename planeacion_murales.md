# Planeación Audioguías Murales Santa Marta

## Información General del Proyecto

**Nombre:** Audioguías murales Santa Marta  
**Objetivo:** Crear una web responsive para que los turistas puedan escuchar las audioguías de los murales seleccionados por el ayuntamiento.  
**Enfoque principal:** Accesibilidad y usabilidad en dispositivos móviles.

## Estructura de Contenido

### Versiones de Audioguía Disponibles
1. **Normativa** - Audio estándar (español e inglés)
2. **Descriptiva** - Audio con descripciones detalladas (solo español)
3. **Fácil** - Audio para discapacidad intelectual (solo español)
4. **Signoguía** - Video en lengua de signos (solo español)

### Idiomas
- **Español:** Todas las versiones disponibles
- **Inglés:** Solo versión normativa (página `/english`)

## Stack Tecnológico

### Frontend (Aplicación Completamente Estática)
- **Astro** - Framework principal para sitio estático. Usar la versión 5 con documentación en https://docs.astro.build/en/getting-started/
- **Tailwind CSS** - Estilos y diseño responsive. Usar la versión 4.1 con documentación en 4.1 en https://tailwindcss.com/docs/installation/framework-guides/astro, https://tailwindcss.com/docs/theme, https://tailwindcss.com/docs/colors, https://tailwindcss.com/docs/adding-custom-styles
- **React** - Componentes interactivos específicos
- **TypeScript** - Para mejor desarrollo y mantenimiento
- **JSON estático** - Archivos de datos para murales y configuración

### Servicios Externos (Sin Backend Propio)
- **Audio.com** - Almacenamiento, streaming y analytics de audios
- **Vimeo** - Almacenamiento, streaming y analytics de videos (signoguía)
- **ableplayer** - Reproductor multimedia accesible
- **Leaflet + OpenStreetMap** - Mapas sin servidor propio
- **Google Analytics 4** - Analytics generales del sitio
- **Netlify/Vercel** - Hosting estático con CDN

### Razones para Audio.com sobre SoundCloud
- Mejor API para embeds sin backend
- Analytics nativos integrados
- Menos restricciones de embed
- Mejor rendimiento en móviles
- Mayor compatibilidad con ableplayer

## Arquitectura de la Aplicación

### Estructura de Directorios
```
src/
├── components/
│   ├── react/
│   │   ├── AudioPlayer.jsx
│   │   ├── MapComponent.jsx
│   │   ├── LanguageSelector.jsx
│   │   └── PlaylistManager.jsx
│   ├── ui/
│   │   ├── Header.astro
│   │   ├── Footer.astro
│   │   ├── Card.astro
│   │   └── Layout.astro
├── pages/
│   ├── index.astro
│   ├── english/
│   │   └── index.astro
│   ├── audioguia-normativa.astro
│   ├── audioguia-descriptiva.astro
│   ├── audioguia-facil.astro
│   └── signoguia.astro
├── data/
│   ├── murals.json
│   ├── routes.json
│   ├── content-es.json
│   └── content-en.json
├── utils/
│   ├── audioAPI.js
│   ├── accessibility.js
│   └── mapUtils.js
├── styles/
│   └── global.css
└── assets/
    ├── images/
    └── icons/
```

## Diseño y Estructura de Páginas

### 1. Página Principal (index.astro)

#### Layout de Cards
- **Selector de idioma** - Botón destacado para cambiar a `/english`
- **Tarjeta Audioguía Fácil** - Rectangular ancha, destacada para accesibilidad
- **Grid de Audioguías** - 2x2 para normativa, descriptiva y signoguía
- **Mapa de Ruta** - Tarjeta rectangular ancha con mapa completo

#### Especificaciones de Cards
```css
/* Tarjeta ancha */
.card-wide {
  @apply w-full col-span-2 aspect-[2/1];
}

/* Tarjeta cuadrada */
.card-square {
  @apply aspect-square;
}

/* Grid principal */
.cards-grid {
  @apply grid grid-cols-2 gap-4 p-4;
}
```

### 2. Página en Inglés (/english)

#### Características Específicas
- Solo versión normativa disponible
- Subtítulos automáticos habilitados
- Navegación optimizada para lectores de pantalla
- Misma funcionalidad de mapa y playlist

### 3. Páginas de Audioguía

#### Estructura Común
1. **Mapa interactivo** - Tarjeta ancha superior
2. **Reproductor actual** - Tarjeta central con cover del mural
3. **Playlist completa** - Lista scrolleable inferior

#### Funcionalidades del Reproductor
- Cover con imagen del mural actual
- Controles: anterior, play/pause, siguiente
- Barra de progreso
- Control de volumen
- Indicador de pista actual en playlist

## Componentes React Detallados

### AudioPlayer.jsx
```javascript
// Funcionalidades requeridas:
- Conexión con Audio.com API
- Control de reproducción (play, pause, skip)
- Sincronización con mapa (highlight pin activo)
- Gestión de playlist
- Controles de accesibilidad
- Progress tracking para analytics
```

### MapComponent.jsx
```javascript
// Funcionalidades requeridas:
- Leaflet + OpenStreetMap integration
- Pins para cada mural
- Highlight de pin activo según audio
- Ruta recomendada visible
- Enlace a Google Maps para indicaciones
- Responsive para móviles
```

### PlaylistManager.jsx
```javascript
// Funcionalidades requeridas:
- Lista de todas las pistas
- Indicador visual de pista actual
- Click para cambiar pista
- Orden según ruta recomendada
- Imágenes thumbnail de murales
```

### LanguageSelector.jsx
```javascript
// Funcionalidades requeridas:
- Toggle entre español e inglés
- Persistencia de selección
- Cambio de todas las etiquetas UI
- Redirección a /english cuando necesario
```

## Elementos Globales

### Header
- **Logo** - Lado izquierdo, enlace a home
- **Menú hamburguesa** - Lado derecho para móvil
- **Navegación** - Enlaces a cada audioguía
- **Descarga PDF** - Enlace al final del menú
- **Controles de accesibilidad** - Alto contraste, tamaño fuente

### Footer
- **Logos ayuntamientos** - Clientes que contrataron
- **Logo empresa** - Nuestra marca
- **Enlaces rápidos** - A cada página principal
- **Información legal** - Copyright, privacidad

## Características de Accesibilidad

### Implementaciones Obligatorias
1. **Alto contraste** - Toggle en header
2. **Tamaño de fuente** - Controles de aumento/reducción
3. **Navegación por teclado** - Tab order correcto
4. **ARIA labels** - Todos los elementos interactivos
5. **Alt text** - Todas las imágenes de murales
6. **Modo sin animaciones** - Para sensibilidad al movimiento
7. **Lectores de pantalla** - Compatibilidad completa

### Audioguía Fácil - Consideraciones Especiales
- **Diseño simplificado** - Menos elementos visuales
- **Iconos grandes** - Botones más accesibles
- **Lenguaje claro** - Textos e instrucciones simples
- **Colores contrastantes** - Mayor diferenciación visual

### Signoguía - Consideraciones Especiales
- **Player de video accesible** - Controles grandes
- **Subtítulos disponibles** - Para apoyo adicional
- **Velocidad ajustable** - Control de reproducción
- **Pantalla completa** - Opción para mejor visualización

## Integración con Servicios Externos (Sin Backend Propio)

### Estructura de Datos Estáticos

#### murals.json
```json
{
  "murals": [
    {
      "id": 1,
      "title": {
        "es": "Mural Centro Histórico",
        "en": "Historic Center Mural"
      },
      "description": {
        "es": "Descripción del mural...",
        "en": "Mural description..."
      },
      "coordinates": [11.2408, -74.2120],
      "order": 1,
      "audio": {
        "normal": {
          "es": "https://audio.com/embed/123-es",
          "en": "https://audio.com/embed/123-en"
        },
        "descriptive": "https://audio.com/embed/124-es",
        "easy": "https://audio.com/embed/125-es"
      },
      "video": {
        "sign": "https://player.vimeo.com/video/123456789"
      },
      "image": "/images/mural-1.webp",
      "alt": {
        "es": "Mural colorido en pared del centro histórico",
        "en": "Colorful mural on historic center wall"
      }
    }
  ],
  "route": {
    "name": {
      "es": "Ruta de Murales Santa Marta",
      "en": "Santa Marta Murals Route"
    },
    "waypoints": [
      [11.2408, -74.2120],
      [11.2415, -74.2130],
      [11.2420, -74.2125]
    ],
    "totalDistance": "2.5 km",
    "estimatedTime": "45 min"
  }
}
```

#### content-es.json / content-en.json
```json
{
  "navigation": {
    "home": "Inicio",
    "audioguides": "Audioguías",
    "map": "Mapa",
    "download": "Descargar PDF"
  },
  "audioguide_types": {
    "normal": "Audioguía Normativa",
    "descriptive": "Audioguía Descriptiva", 
    "easy": "Audioguía Fácil",
    "sign": "Signoguía"
  },
  "controls": {
    "play": "Reproducir",
    "pause": "Pausar",
    "previous": "Anterior",
    "next": "Siguiente",
    "volume": "Volumen"
  }
}
```

### Audio.com Integration (Solo Frontend)
```javascript
// Configuración de embeds estáticos
const audioConfig = {
  // URLs de embed proporcionadas por Audio.com
  baseURL: 'https://audio.com/embed/',
  // Sin API calls, solo embeds directos
  // Analytics automáticos de Audio.com
}

// Implementación en componente React
const AudioPlayer = ({ audioUrl, title }) => {
  return (
    <iframe 
      src={audioUrl}
      width="100%" 
      height="166" 
      frameBorder="no"
      allow="autoplay"
      title={title}
    />
  );
};
```

### Vimeo Integration (Solo Frontend)
```javascript
// Configuración de embeds responsivos
const vimeoConfig = {
  baseURL: 'https://player.vimeo.com/video/',
  // Parámetros para accesibilidad
  params: '?controls=1&keyboard=1&title=1&byline=0&portrait=0'
}

// Embed responsivo
const VideoPlayer = ({ videoId, title }) => {
  return (
    <div className="aspect-video">
      <iframe 
        src={`${vimeoConfig.baseURL}${videoId}${vimeoConfig.params}`}
        width="100%" 
        height="100%"
        allow="fullscreen"
        title={title}
      />
    </div>
  );
};
```

### Leaflet + OpenStreetMap (Cliente-Side)
```javascript
// Configuración completamente frontend
const mapConfig = {
  center: [11.2408, -74.2120], // Santa Marta
  zoom: 15,
  maxZoom: 18,
  attribution: '© OpenStreetMap contributors'
}

// Implementación sin servidor
const MapComponent = ({ murals, route }) => {
  useEffect(() => {
    const map = L.map('map').setView(mapConfig.center, mapConfig.zoom);
    
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: mapConfig.attribution
    }).addTo(map);
    
    // Añadir markers desde datos estáticos
    murals.forEach(mural => {
      L.marker(mural.coordinates)
        .addTo(map)
        .bindPopup(`
          <strong>${mural.title}</strong><br>
          <a href="https://maps.google.com/maps?daddr=${mural.coordinates[0]},${mural.coordinates[1]}" target="_blank">
            Cómo llegar
          </a>
        `);
    });
    
    // Añadir ruta desde datos estáticos
    L.polyline(route.waypoints, {color: 'red'}).addTo(map);
  }, []);
  
  return <div id="map" className="h-64 w-full rounded-lg" />;
};
```

## Optimizaciones de Performance

### Implementaciones Requeridas
1. **Lazy loading** - Imágenes de murales
2. **Service Worker** - Cacheo básico para offline
3. **Precarga inteligente** - Siguiente audio mientras reproduce actual
4. **Compresión de imágenes** - WebP con fallback
5. **Minificación** - CSS y JS
6. **Critical CSS** - Above the fold optimization

### Métricas a Implementar
- **Tiempo de carga inicial**
- **Time to Interactive**
- **Largest Contentful Paint**
- **Cumulative Layout Shift**

## Analytics y Métricas (Sin Backend Propio)

### Servicios de Analytics Externos
1. **Google Analytics 4** - Métricas generales del sitio
   - Páginas visitadas
   - Tiempo en sitio
   - Dispositivos utilizados
   - Idioma del navegador
   - Flujo de usuarios entre páginas

2. **Audio.com Analytics** - Datos nativos de reproducción
   - Reproducciones por pista
   - Tiempo de escucha real
   - Tasa de finalización
   - Dispositivos de reproducción

3. **Vimeo Analytics** - Métricas de videos (signoguía)
   - Visualizaciones de videos
   - Tiempo de visualización
   - Interacciones con controles

### Implementación de Tracking Frontend
```javascript
// Google Analytics 4 Events
const trackAudioPlay = (muralId, audioType, language) => {
  gtag('event', 'audio_play', {
    'mural_id': muralId,
    'audio_type': audioType,
    'language': language,
    'page_location': window.location.href
  });
};

const trackRouteView = (routeType) => {
  gtag('event', 'route_view', {
    'route_type': routeType,
    'timestamp': new Date().toISOString()
  });
};

// Implementación en componentes React
const AudioPlayer = ({ muralId, audioType, language }) => {
  const handlePlay = () => {
    trackAudioPlay(muralId, audioType, language);
    // Lógica de reproducción
  };
};
```

### Dashboard de Métricas
- **Google Analytics Dashboard** - Vista consolidada para el cliente
- **Audio.com Dashboard** - Métricas específicas de audio
- **Vimeo Dashboard** - Métricas específicas de video
- **Reportes mensuales** - Compilación manual de datos

## Consideraciones de Desarrollo

### Proceso de Implementación Sugerido
1. **Setup inicial** - Astro + Tailwind + TypeScript
2. **Estructura de datos** - Archivos JSON estáticos
3. **Componentes base** - Header, Footer, Layout
4. **Página principal** - Grid de cards y navegación
5. **Integración de mapa** - Leaflet con datos estáticos
6. **Reproductor de audio** - Embeds de Audio.com y Vimeo
7. **Páginas de audioguía** - Layout y funcionalidad
8. **Implementación bilingüe** - Español e inglés
9. **Accesibilidad** - ARIA, contraste, navegación
10. **Optimización** - Performance y SEO
11. **Analytics** - Google Analytics 4 y tracking
12. **Testing** - Dispositivos y accesibilidad
13. **Deploy estático** - Netlify/Vercel con CDN

### Testing Requerido
- **Dispositivos móviles** - iOS Safari, Android Chrome
- **Lectores de pantalla** - NVDA, JAWS, VoiceOver
- **Navegación por teclado** - Tab order completo
- **Diferentes velocidades de conexión** - 3G, 4G, WiFi
- **Navegadores** - Chrome, Firefox, Safari, Edge

## Contenido y Assets Necesarios

### Audio Content
- **Archivos de audio** - Subidos a Audio.com
- **Metadatos** - Título, descripción, duración, GPS
- **Covers** - Imagen de cada mural (optimizadas)

### Video Content (Signoguía)
- **Videos en lengua de signos** - Subidos a Vimeo
- **Subtítulos** - Archivos SRT para cada video
- **Thumbnails** - Imágenes preview

### Imágenes
- **Logos** - Ayuntamientos, empresa, ruta turística
- **Murales** - Fotografías en alta calidad
- **Iconos** - Accesibilidad, navegación, controles
- **Mapas** - Capturas de respaldo si es necesario

### Textos y Traducciones
- **Contenido español** - Todas las secciones
- **Contenido inglés** - Solo página /english
- **Términos accesibles** - Para audioguía fácil
- **Metadatos SEO** - Títulos, descripciones, keywords

## Notas de Desarrollo Final

### Prioridades de Implementación
1. **Funcionalidad básica** - Reproducción y navegación
2. **Responsive design** - Mobile first approach
3. **Accesibilidad** - Cumplimiento WCAG 2.1 AA
4. **Performance** - Carga rápida en móviles
5. **Analytics** - Tracking completo
6. **Testing exhaustivo** - Todos los dispositivos objetivo

### Consideraciones Post-Launch
- **Monitoreo con herramientas externas** - Google Analytics, Audio.com, Vimeo dashboards
- **Feedback de usuarios** - Formularios simples o email de contacto
- **Actualizaciones de contenido** - Edición manual de archivos JSON
- **Mantenimiento** - Solo dependencias frontend y embeds externos
- **Escalabilidad** - Fácil añadir nuevos murales editando JSON
- **Backup de datos** - Archivos JSON en repositorio Git