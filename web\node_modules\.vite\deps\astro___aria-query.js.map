{"version": 3, "sources": ["../../aria-query/lib/util/iteratorProxy.js", "../../aria-query/lib/util/iterationDecorator.js", "../../aria-query/lib/ariaPropsMap.js", "../../aria-query/lib/domMap.js", "../../aria-query/lib/etc/roles/abstract/commandRole.js", "../../aria-query/lib/etc/roles/abstract/compositeRole.js", "../../aria-query/lib/etc/roles/abstract/inputRole.js", "../../aria-query/lib/etc/roles/abstract/landmarkRole.js", "../../aria-query/lib/etc/roles/abstract/rangeRole.js", "../../aria-query/lib/etc/roles/abstract/roletypeRole.js", "../../aria-query/lib/etc/roles/abstract/sectionRole.js", "../../aria-query/lib/etc/roles/abstract/sectionheadRole.js", "../../aria-query/lib/etc/roles/abstract/selectRole.js", "../../aria-query/lib/etc/roles/abstract/structureRole.js", "../../aria-query/lib/etc/roles/abstract/widgetRole.js", "../../aria-query/lib/etc/roles/abstract/windowRole.js", "../../aria-query/lib/etc/roles/ariaAbstractRoles.js", "../../aria-query/lib/etc/roles/literal/alertRole.js", "../../aria-query/lib/etc/roles/literal/alertdialogRole.js", "../../aria-query/lib/etc/roles/literal/applicationRole.js", "../../aria-query/lib/etc/roles/literal/articleRole.js", "../../aria-query/lib/etc/roles/literal/bannerRole.js", "../../aria-query/lib/etc/roles/literal/blockquoteRole.js", "../../aria-query/lib/etc/roles/literal/buttonRole.js", "../../aria-query/lib/etc/roles/literal/captionRole.js", "../../aria-query/lib/etc/roles/literal/cellRole.js", "../../aria-query/lib/etc/roles/literal/checkboxRole.js", "../../aria-query/lib/etc/roles/literal/codeRole.js", "../../aria-query/lib/etc/roles/literal/columnheaderRole.js", "../../aria-query/lib/etc/roles/literal/comboboxRole.js", "../../aria-query/lib/etc/roles/literal/complementaryRole.js", "../../aria-query/lib/etc/roles/literal/contentinfoRole.js", "../../aria-query/lib/etc/roles/literal/definitionRole.js", "../../aria-query/lib/etc/roles/literal/deletionRole.js", "../../aria-query/lib/etc/roles/literal/dialogRole.js", "../../aria-query/lib/etc/roles/literal/directoryRole.js", "../../aria-query/lib/etc/roles/literal/documentRole.js", "../../aria-query/lib/etc/roles/literal/emphasisRole.js", "../../aria-query/lib/etc/roles/literal/feedRole.js", "../../aria-query/lib/etc/roles/literal/figureRole.js", "../../aria-query/lib/etc/roles/literal/formRole.js", "../../aria-query/lib/etc/roles/literal/genericRole.js", "../../aria-query/lib/etc/roles/literal/gridRole.js", "../../aria-query/lib/etc/roles/literal/gridcellRole.js", "../../aria-query/lib/etc/roles/literal/groupRole.js", "../../aria-query/lib/etc/roles/literal/headingRole.js", "../../aria-query/lib/etc/roles/literal/imgRole.js", "../../aria-query/lib/etc/roles/literal/insertionRole.js", "../../aria-query/lib/etc/roles/literal/linkRole.js", "../../aria-query/lib/etc/roles/literal/listRole.js", "../../aria-query/lib/etc/roles/literal/listboxRole.js", "../../aria-query/lib/etc/roles/literal/listitemRole.js", "../../aria-query/lib/etc/roles/literal/logRole.js", "../../aria-query/lib/etc/roles/literal/mainRole.js", "../../aria-query/lib/etc/roles/literal/markRole.js", "../../aria-query/lib/etc/roles/literal/marqueeRole.js", "../../aria-query/lib/etc/roles/literal/mathRole.js", "../../aria-query/lib/etc/roles/literal/menuRole.js", "../../aria-query/lib/etc/roles/literal/menubarRole.js", "../../aria-query/lib/etc/roles/literal/menuitemRole.js", "../../aria-query/lib/etc/roles/literal/menuitemcheckboxRole.js", "../../aria-query/lib/etc/roles/literal/menuitemradioRole.js", "../../aria-query/lib/etc/roles/literal/meterRole.js", "../../aria-query/lib/etc/roles/literal/navigationRole.js", "../../aria-query/lib/etc/roles/literal/noneRole.js", "../../aria-query/lib/etc/roles/literal/noteRole.js", "../../aria-query/lib/etc/roles/literal/optionRole.js", "../../aria-query/lib/etc/roles/literal/paragraphRole.js", "../../aria-query/lib/etc/roles/literal/presentationRole.js", "../../aria-query/lib/etc/roles/literal/progressbarRole.js", "../../aria-query/lib/etc/roles/literal/radioRole.js", "../../aria-query/lib/etc/roles/literal/radiogroupRole.js", "../../aria-query/lib/etc/roles/literal/regionRole.js", "../../aria-query/lib/etc/roles/literal/rowRole.js", "../../aria-query/lib/etc/roles/literal/rowgroupRole.js", "../../aria-query/lib/etc/roles/literal/rowheaderRole.js", "../../aria-query/lib/etc/roles/literal/scrollbarRole.js", "../../aria-query/lib/etc/roles/literal/searchRole.js", "../../aria-query/lib/etc/roles/literal/searchboxRole.js", "../../aria-query/lib/etc/roles/literal/separatorRole.js", "../../aria-query/lib/etc/roles/literal/sliderRole.js", "../../aria-query/lib/etc/roles/literal/spinbuttonRole.js", "../../aria-query/lib/etc/roles/literal/statusRole.js", "../../aria-query/lib/etc/roles/literal/strongRole.js", "../../aria-query/lib/etc/roles/literal/subscriptRole.js", "../../aria-query/lib/etc/roles/literal/superscriptRole.js", "../../aria-query/lib/etc/roles/literal/switchRole.js", "../../aria-query/lib/etc/roles/literal/tabRole.js", "../../aria-query/lib/etc/roles/literal/tableRole.js", "../../aria-query/lib/etc/roles/literal/tablistRole.js", "../../aria-query/lib/etc/roles/literal/tabpanelRole.js", "../../aria-query/lib/etc/roles/literal/termRole.js", "../../aria-query/lib/etc/roles/literal/textboxRole.js", "../../aria-query/lib/etc/roles/literal/timeRole.js", "../../aria-query/lib/etc/roles/literal/timerRole.js", "../../aria-query/lib/etc/roles/literal/toolbarRole.js", "../../aria-query/lib/etc/roles/literal/tooltipRole.js", "../../aria-query/lib/etc/roles/literal/treeRole.js", "../../aria-query/lib/etc/roles/literal/treegridRole.js", "../../aria-query/lib/etc/roles/literal/treeitemRole.js", "../../aria-query/lib/etc/roles/ariaLiteralRoles.js", "../../aria-query/lib/etc/roles/dpub/docAbstractRole.js", "../../aria-query/lib/etc/roles/dpub/docAcknowledgmentsRole.js", "../../aria-query/lib/etc/roles/dpub/docAfterwordRole.js", "../../aria-query/lib/etc/roles/dpub/docAppendixRole.js", "../../aria-query/lib/etc/roles/dpub/docBacklinkRole.js", "../../aria-query/lib/etc/roles/dpub/docBiblioentryRole.js", "../../aria-query/lib/etc/roles/dpub/docBibliographyRole.js", "../../aria-query/lib/etc/roles/dpub/docBibliorefRole.js", "../../aria-query/lib/etc/roles/dpub/docChapterRole.js", "../../aria-query/lib/etc/roles/dpub/docColophonRole.js", "../../aria-query/lib/etc/roles/dpub/docConclusionRole.js", "../../aria-query/lib/etc/roles/dpub/docCoverRole.js", "../../aria-query/lib/etc/roles/dpub/docCreditRole.js", "../../aria-query/lib/etc/roles/dpub/docCreditsRole.js", "../../aria-query/lib/etc/roles/dpub/docDedicationRole.js", "../../aria-query/lib/etc/roles/dpub/docEndnoteRole.js", "../../aria-query/lib/etc/roles/dpub/docEndnotesRole.js", "../../aria-query/lib/etc/roles/dpub/docEpigraphRole.js", "../../aria-query/lib/etc/roles/dpub/docEpilogueRole.js", "../../aria-query/lib/etc/roles/dpub/docErrataRole.js", "../../aria-query/lib/etc/roles/dpub/docExampleRole.js", "../../aria-query/lib/etc/roles/dpub/docFootnoteRole.js", "../../aria-query/lib/etc/roles/dpub/docForewordRole.js", "../../aria-query/lib/etc/roles/dpub/docGlossaryRole.js", "../../aria-query/lib/etc/roles/dpub/docGlossrefRole.js", "../../aria-query/lib/etc/roles/dpub/docIndexRole.js", "../../aria-query/lib/etc/roles/dpub/docIntroductionRole.js", "../../aria-query/lib/etc/roles/dpub/docNoterefRole.js", "../../aria-query/lib/etc/roles/dpub/docNoticeRole.js", "../../aria-query/lib/etc/roles/dpub/docPagebreakRole.js", "../../aria-query/lib/etc/roles/dpub/docPagefooterRole.js", "../../aria-query/lib/etc/roles/dpub/docPageheaderRole.js", "../../aria-query/lib/etc/roles/dpub/docPagelistRole.js", "../../aria-query/lib/etc/roles/dpub/docPartRole.js", "../../aria-query/lib/etc/roles/dpub/docPrefaceRole.js", "../../aria-query/lib/etc/roles/dpub/docPrologueRole.js", "../../aria-query/lib/etc/roles/dpub/docPullquoteRole.js", "../../aria-query/lib/etc/roles/dpub/docQnaRole.js", "../../aria-query/lib/etc/roles/dpub/docSubtitleRole.js", "../../aria-query/lib/etc/roles/dpub/docTipRole.js", "../../aria-query/lib/etc/roles/dpub/docTocRole.js", "../../aria-query/lib/etc/roles/ariaDpubRoles.js", "../../aria-query/lib/etc/roles/graphics/graphicsDocumentRole.js", "../../aria-query/lib/etc/roles/graphics/graphicsObjectRole.js", "../../aria-query/lib/etc/roles/graphics/graphicsSymbolRole.js", "../../aria-query/lib/etc/roles/ariaGraphicsRoles.js", "../../aria-query/lib/rolesMap.js", "../../aria-query/lib/elementRoleMap.js", "../../aria-query/lib/roleElementMap.js", "../../aria-query/lib/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\n// eslint-disable-next-line no-unused-vars\nfunction iteratorProxy() {\n  var values = this;\n  var index = 0;\n  var iter = {\n    '@@iterator': function iterator() {\n      return iter;\n    },\n    next: function next() {\n      if (index < values.length) {\n        var value = values[index];\n        index = index + 1;\n        return {\n          done: false,\n          value: value\n        };\n      } else {\n        return {\n          done: true\n        };\n      }\n    }\n  };\n  return iter;\n}\nvar _default = exports.default = iteratorProxy;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = iterationDecorator;\nvar _iteratorProxy = _interopRequireDefault(require(\"./iteratorProxy\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction iterationDecorator(collection, entries) {\n  if (typeof Symbol === 'function' && _typeof(Symbol.iterator) === 'symbol') {\n    Object.defineProperty(collection, Symbol.iterator, {\n      value: _iteratorProxy.default.bind(entries)\n    });\n  }\n  return collection;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _iterationDecorator = _interopRequireDefault(require(\"./util/iterationDecorator\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nvar properties = [['aria-activedescendant', {\n  'type': 'id'\n}], ['aria-atomic', {\n  'type': 'boolean'\n}], ['aria-autocomplete', {\n  'type': 'token',\n  'values': ['inline', 'list', 'both', 'none']\n}], ['aria-braillelabel', {\n  'type': 'string'\n}], ['aria-brailleroledescription', {\n  'type': 'string'\n}], ['aria-busy', {\n  'type': 'boolean'\n}], ['aria-checked', {\n  'type': 'tristate'\n}], ['aria-colcount', {\n  type: 'integer'\n}], ['aria-colindex', {\n  type: 'integer'\n}], ['aria-colspan', {\n  type: 'integer'\n}], ['aria-controls', {\n  'type': 'idlist'\n}], ['aria-current', {\n  type: 'token',\n  values: ['page', 'step', 'location', 'date', 'time', true, false]\n}], ['aria-describedby', {\n  'type': 'idlist'\n}], ['aria-description', {\n  'type': 'string'\n}], ['aria-details', {\n  'type': 'id'\n}], ['aria-disabled', {\n  'type': 'boolean'\n}], ['aria-dropeffect', {\n  'type': 'tokenlist',\n  'values': ['copy', 'execute', 'link', 'move', 'none', 'popup']\n}], ['aria-errormessage', {\n  'type': 'id'\n}], ['aria-expanded', {\n  'type': 'boolean',\n  'allowundefined': true\n}], ['aria-flowto', {\n  'type': 'idlist'\n}], ['aria-grabbed', {\n  'type': 'boolean',\n  'allowundefined': true\n}], ['aria-haspopup', {\n  'type': 'token',\n  'values': [false, true, 'menu', 'listbox', 'tree', 'grid', 'dialog']\n}], ['aria-hidden', {\n  'type': 'boolean',\n  'allowundefined': true\n}], ['aria-invalid', {\n  'type': 'token',\n  'values': ['grammar', false, 'spelling', true]\n}], ['aria-keyshortcuts', {\n  type: 'string'\n}], ['aria-label', {\n  'type': 'string'\n}], ['aria-labelledby', {\n  'type': 'idlist'\n}], ['aria-level', {\n  'type': 'integer'\n}], ['aria-live', {\n  'type': 'token',\n  'values': ['assertive', 'off', 'polite']\n}], ['aria-modal', {\n  type: 'boolean'\n}], ['aria-multiline', {\n  'type': 'boolean'\n}], ['aria-multiselectable', {\n  'type': 'boolean'\n}], ['aria-orientation', {\n  'type': 'token',\n  'values': ['vertical', 'undefined', 'horizontal']\n}], ['aria-owns', {\n  'type': 'idlist'\n}], ['aria-placeholder', {\n  type: 'string'\n}], ['aria-posinset', {\n  'type': 'integer'\n}], ['aria-pressed', {\n  'type': 'tristate'\n}], ['aria-readonly', {\n  'type': 'boolean'\n}], ['aria-relevant', {\n  'type': 'tokenlist',\n  'values': ['additions', 'all', 'removals', 'text']\n}], ['aria-required', {\n  'type': 'boolean'\n}], ['aria-roledescription', {\n  type: 'string'\n}], ['aria-rowcount', {\n  type: 'integer'\n}], ['aria-rowindex', {\n  type: 'integer'\n}], ['aria-rowspan', {\n  type: 'integer'\n}], ['aria-selected', {\n  'type': 'boolean',\n  'allowundefined': true\n}], ['aria-setsize', {\n  'type': 'integer'\n}], ['aria-sort', {\n  'type': 'token',\n  'values': ['ascending', 'descending', 'none', 'other']\n}], ['aria-valuemax', {\n  'type': 'number'\n}], ['aria-valuemin', {\n  'type': 'number'\n}], ['aria-valuenow', {\n  'type': 'number'\n}], ['aria-valuetext', {\n  'type': 'string'\n}]];\nvar ariaPropsMap = {\n  entries: function entries() {\n    return properties;\n  },\n  forEach: function forEach(fn) {\n    var thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    for (var _i = 0, _properties = properties; _i < _properties.length; _i++) {\n      var _properties$_i = _slicedToArray(_properties[_i], 2),\n        key = _properties$_i[0],\n        values = _properties$_i[1];\n      fn.call(thisArg, values, key, properties);\n    }\n  },\n  get: function get(key) {\n    var item = properties.filter(function (tuple) {\n      return tuple[0] === key ? true : false;\n    })[0];\n    return item && item[1];\n  },\n  has: function has(key) {\n    return !!ariaPropsMap.get(key);\n  },\n  keys: function keys() {\n    return properties.map(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 1),\n        key = _ref2[0];\n      return key;\n    });\n  },\n  values: function values() {\n    return properties.map(function (_ref3) {\n      var _ref4 = _slicedToArray(_ref3, 2),\n        values = _ref4[1];\n      return values;\n    });\n  }\n};\nvar _default = exports.default = (0, _iterationDecorator.default)(ariaPropsMap, ariaPropsMap.entries());", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _iterationDecorator = _interopRequireDefault(require(\"./util/iterationDecorator\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nvar dom = [['a', {\n  reserved: false\n}], ['abbr', {\n  reserved: false\n}], ['acronym', {\n  reserved: false\n}], ['address', {\n  reserved: false\n}], ['applet', {\n  reserved: false\n}], ['area', {\n  reserved: false\n}], ['article', {\n  reserved: false\n}], ['aside', {\n  reserved: false\n}], ['audio', {\n  reserved: false\n}], ['b', {\n  reserved: false\n}], ['base', {\n  reserved: true\n}], ['bdi', {\n  reserved: false\n}], ['bdo', {\n  reserved: false\n}], ['big', {\n  reserved: false\n}], ['blink', {\n  reserved: false\n}], ['blockquote', {\n  reserved: false\n}], ['body', {\n  reserved: false\n}], ['br', {\n  reserved: false\n}], ['button', {\n  reserved: false\n}], ['canvas', {\n  reserved: false\n}], ['caption', {\n  reserved: false\n}], ['center', {\n  reserved: false\n}], ['cite', {\n  reserved: false\n}], ['code', {\n  reserved: false\n}], ['col', {\n  reserved: true\n}], ['colgroup', {\n  reserved: true\n}], ['content', {\n  reserved: false\n}], ['data', {\n  reserved: false\n}], ['datalist', {\n  reserved: false\n}], ['dd', {\n  reserved: false\n}], ['del', {\n  reserved: false\n}], ['details', {\n  reserved: false\n}], ['dfn', {\n  reserved: false\n}], ['dialog', {\n  reserved: false\n}], ['dir', {\n  reserved: false\n}], ['div', {\n  reserved: false\n}], ['dl', {\n  reserved: false\n}], ['dt', {\n  reserved: false\n}], ['em', {\n  reserved: false\n}], ['embed', {\n  reserved: false\n}], ['fieldset', {\n  reserved: false\n}], ['figcaption', {\n  reserved: false\n}], ['figure', {\n  reserved: false\n}], ['font', {\n  reserved: false\n}], ['footer', {\n  reserved: false\n}], ['form', {\n  reserved: false\n}], ['frame', {\n  reserved: false\n}], ['frameset', {\n  reserved: false\n}], ['h1', {\n  reserved: false\n}], ['h2', {\n  reserved: false\n}], ['h3', {\n  reserved: false\n}], ['h4', {\n  reserved: false\n}], ['h5', {\n  reserved: false\n}], ['h6', {\n  reserved: false\n}], ['head', {\n  reserved: true\n}], ['header', {\n  reserved: false\n}], ['hgroup', {\n  reserved: false\n}], ['hr', {\n  reserved: false\n}], ['html', {\n  reserved: true\n}], ['i', {\n  reserved: false\n}], ['iframe', {\n  reserved: false\n}], ['img', {\n  reserved: false\n}], ['input', {\n  reserved: false\n}], ['ins', {\n  reserved: false\n}], ['kbd', {\n  reserved: false\n}], ['keygen', {\n  reserved: false\n}], ['label', {\n  reserved: false\n}], ['legend', {\n  reserved: false\n}], ['li', {\n  reserved: false\n}], ['link', {\n  reserved: true\n}], ['main', {\n  reserved: false\n}], ['map', {\n  reserved: false\n}], ['mark', {\n  reserved: false\n}], ['marquee', {\n  reserved: false\n}], ['menu', {\n  reserved: false\n}], ['menuitem', {\n  reserved: false\n}], ['meta', {\n  reserved: true\n}], ['meter', {\n  reserved: false\n}], ['nav', {\n  reserved: false\n}], ['noembed', {\n  reserved: true\n}], ['noscript', {\n  reserved: true\n}], ['object', {\n  reserved: false\n}], ['ol', {\n  reserved: false\n}], ['optgroup', {\n  reserved: false\n}], ['option', {\n  reserved: false\n}], ['output', {\n  reserved: false\n}], ['p', {\n  reserved: false\n}], ['param', {\n  reserved: true\n}], ['picture', {\n  reserved: true\n}], ['pre', {\n  reserved: false\n}], ['progress', {\n  reserved: false\n}], ['q', {\n  reserved: false\n}], ['rp', {\n  reserved: false\n}], ['rt', {\n  reserved: false\n}], ['rtc', {\n  reserved: false\n}], ['ruby', {\n  reserved: false\n}], ['s', {\n  reserved: false\n}], ['samp', {\n  reserved: false\n}], ['script', {\n  reserved: true\n}], ['section', {\n  reserved: false\n}], ['select', {\n  reserved: false\n}], ['small', {\n  reserved: false\n}], ['source', {\n  reserved: true\n}], ['spacer', {\n  reserved: false\n}], ['span', {\n  reserved: false\n}], ['strike', {\n  reserved: false\n}], ['strong', {\n  reserved: false\n}], ['style', {\n  reserved: true\n}], ['sub', {\n  reserved: false\n}], ['summary', {\n  reserved: false\n}], ['sup', {\n  reserved: false\n}], ['table', {\n  reserved: false\n}], ['tbody', {\n  reserved: false\n}], ['td', {\n  reserved: false\n}], ['textarea', {\n  reserved: false\n}], ['tfoot', {\n  reserved: false\n}], ['th', {\n  reserved: false\n}], ['thead', {\n  reserved: false\n}], ['time', {\n  reserved: false\n}], ['title', {\n  reserved: true\n}], ['tr', {\n  reserved: false\n}], ['track', {\n  reserved: true\n}], ['tt', {\n  reserved: false\n}], ['u', {\n  reserved: false\n}], ['ul', {\n  reserved: false\n}], ['var', {\n  reserved: false\n}], ['video', {\n  reserved: false\n}], ['wbr', {\n  reserved: false\n}], ['xmp', {\n  reserved: false\n}]];\nvar domMap = {\n  entries: function entries() {\n    return dom;\n  },\n  forEach: function forEach(fn) {\n    var thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    for (var _i = 0, _dom = dom; _i < _dom.length; _i++) {\n      var _dom$_i = _slicedToArray(_dom[_i], 2),\n        key = _dom$_i[0],\n        values = _dom$_i[1];\n      fn.call(thisArg, values, key, dom);\n    }\n  },\n  get: function get(key) {\n    var item = dom.filter(function (tuple) {\n      return tuple[0] === key ? true : false;\n    })[0];\n    return item && item[1];\n  },\n  has: function has(key) {\n    return !!domMap.get(key);\n  },\n  keys: function keys() {\n    return dom.map(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 1),\n        key = _ref2[0];\n      return key;\n    });\n  },\n  values: function values() {\n    return dom.map(function (_ref3) {\n      var _ref4 = _slicedToArray(_ref3, 2),\n        values = _ref4[1];\n      return values;\n    });\n  }\n};\nvar _default = exports.default = (0, _iterationDecorator.default)(domMap, domMap.entries());", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar commandRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget']]\n};\nvar _default = exports.default = commandRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar compositeRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-activedescendant': null,\n    'aria-disabled': null\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget']]\n};\nvar _default = exports.default = compositeRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar inputRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'input'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget']]\n};\nvar _default = exports.default = inputRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar landmarkRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = landmarkRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar rangeRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-valuemax': null,\n    'aria-valuemin': null,\n    'aria-valuenow': null\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = exports.default = rangeRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar roletypeRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: [],\n  prohibitedProps: [],\n  props: {\n    'aria-atomic': null,\n    'aria-busy': null,\n    'aria-controls': null,\n    'aria-current': null,\n    'aria-describedby': null,\n    'aria-details': null,\n    'aria-dropeffect': null,\n    'aria-flowto': null,\n    'aria-grabbed': null,\n    'aria-hidden': null,\n    'aria-keyshortcuts': null,\n    'aria-label': null,\n    'aria-labelledby': null,\n    'aria-live': null,\n    'aria-owns': null,\n    'aria-relevant': null,\n    'aria-roledescription': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'role'\n    },\n    module: 'XHTML'\n  }, {\n    concept: {\n      name: 'type'\n    },\n    module: 'Dublin Core'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: []\n};\nvar _default = exports.default = roletypeRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar sectionRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: [],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'frontmatter'\n    },\n    module: 'DTB'\n  }, {\n    concept: {\n      name: 'level'\n    },\n    module: 'DTB'\n  }, {\n    concept: {\n      name: 'level'\n    },\n    module: 'SMIL'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = exports.default = sectionRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar sectionheadRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = exports.default = sectionheadRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar selectRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-orientation': null\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite'], ['roletype', 'structure', 'section', 'group']]\n};\nvar _default = exports.default = selectRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar structureRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: [],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype']]\n};\nvar _default = exports.default = structureRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar widgetRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: [],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype']]\n};\nvar _default = exports.default = widgetRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar windowRole = {\n  abstract: true,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-modal': null\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype']]\n};\nvar _default = exports.default = windowRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _commandRole = _interopRequireDefault(require(\"./abstract/commandRole\"));\nvar _compositeRole = _interopRequireDefault(require(\"./abstract/compositeRole\"));\nvar _inputRole = _interopRequireDefault(require(\"./abstract/inputRole\"));\nvar _landmarkRole = _interopRequireDefault(require(\"./abstract/landmarkRole\"));\nvar _rangeRole = _interopRequireDefault(require(\"./abstract/rangeRole\"));\nvar _roletypeRole = _interopRequireDefault(require(\"./abstract/roletypeRole\"));\nvar _sectionRole = _interopRequireDefault(require(\"./abstract/sectionRole\"));\nvar _sectionheadRole = _interopRequireDefault(require(\"./abstract/sectionheadRole\"));\nvar _selectRole = _interopRequireDefault(require(\"./abstract/selectRole\"));\nvar _structureRole = _interopRequireDefault(require(\"./abstract/structureRole\"));\nvar _widgetRole = _interopRequireDefault(require(\"./abstract/widgetRole\"));\nvar _windowRole = _interopRequireDefault(require(\"./abstract/windowRole\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nvar ariaAbstractRoles = [['command', _commandRole.default], ['composite', _compositeRole.default], ['input', _inputRole.default], ['landmark', _landmarkRole.default], ['range', _rangeRole.default], ['roletype', _roletypeRole.default], ['section', _sectionRole.default], ['sectionhead', _sectionheadRole.default], ['select', _selectRole.default], ['structure', _structureRole.default], ['widget', _widgetRole.default], ['window', _windowRole.default]];\nvar _default = exports.default = ariaAbstractRoles;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar alertRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-atomic': 'true',\n    'aria-live': 'assertive'\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'alert'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = alertRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar alertdialogRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'alert'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'alert'], ['roletype', 'window', 'dialog']]\n};\nvar _default = exports.default = alertdialogRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar applicationRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-activedescendant': null,\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'Device Independence Delivery Unit'\n    }\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = exports.default = applicationRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar articleRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-posinset': null,\n    'aria-setsize': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'article'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'document']]\n};\nvar _default = exports.default = articleRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar bannerRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      constraints: ['scoped to the body element'],\n      name: 'header'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = bannerRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar blockquoteRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'blockquote'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = blockquoteRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar buttonRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-pressed': null\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'button'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'image'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'reset'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'submit'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'button'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'trigger'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'command']]\n};\nvar _default = exports.default = buttonRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar captionRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'caption'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: ['figure', 'grid', 'table'],\n  requiredContextRole: ['figure', 'grid', 'table'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = captionRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar cellRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-colindex': null,\n    'aria-colspan': null,\n    'aria-rowindex': null,\n    'aria-rowspan': null\n  },\n  relatedConcepts: [{\n    concept: {\n      constraints: ['ancestor table element has table role'],\n      name: 'td'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: ['row'],\n  requiredContextRole: ['row'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = cellRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar checkboxRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-checked': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-invalid': null,\n    'aria-readonly': null,\n    'aria-required': null\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'checkbox'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'option'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-checked': null\n  },\n  superClass: [['roletype', 'widget', 'input']]\n};\nvar _default = exports.default = checkboxRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar codeRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'code'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = codeRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar columnheaderRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-sort': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'th'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        name: 'scope',\n        value: 'col'\n      }],\n      name: 'th'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        name: 'scope',\n        value: 'colgroup'\n      }],\n      name: 'th'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: ['row'],\n  requiredContextRole: ['row'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'cell'], ['roletype', 'structure', 'section', 'cell', 'gridcell'], ['roletype', 'widget', 'gridcell'], ['roletype', 'structure', 'sectionhead']]\n};\nvar _default = exports.default = columnheaderRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar comboboxRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-activedescendant': null,\n    'aria-autocomplete': null,\n    'aria-errormessage': null,\n    'aria-invalid': null,\n    'aria-readonly': null,\n    'aria-required': null,\n    'aria-expanded': 'false',\n    'aria-haspopup': 'listbox'\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'email'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'search'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'tel'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'text'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'url'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'url'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['undefined'],\n        name: 'multiple'\n      }, {\n        constraints: ['undefined'],\n        name: 'size'\n      }],\n      constraints: ['the multiple attribute is not set and the size attribute does not have a value greater than 1'],\n      name: 'select'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'select'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-controls': null,\n    'aria-expanded': 'false'\n  },\n  superClass: [['roletype', 'widget', 'input']]\n};\nvar _default = exports.default = comboboxRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar complementaryRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      constraints: ['scoped to the body element', 'scoped to the main element'],\n      name: 'aside'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'aria-label'\n      }],\n      constraints: ['scoped to a sectioning content element', 'scoped to a sectioning root element other than body'],\n      name: 'aside'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'aria-labelledby'\n      }],\n      constraints: ['scoped to a sectioning content element', 'scoped to a sectioning root element other than body'],\n      name: 'aside'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = complementaryRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar contentinfoRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      constraints: ['scoped to the body element'],\n      name: 'footer'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = contentinfoRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar definitionRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'dd'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = definitionRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar deletionRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'del'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = deletionRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar dialogRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'dialog'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'window']]\n};\nvar _default = exports.default = dialogRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar directoryRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    module: 'DAISY Guide'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'list']]\n};\nvar _default = exports.default = directoryRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar documentRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'Device Independence Delivery Unit'\n    }\n  }, {\n    concept: {\n      name: 'html'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = exports.default = documentRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar emphasisRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'em'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = emphasisRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar feedRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['article']],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'list']]\n};\nvar _default = exports.default = feedRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar figureRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'figure'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = figureRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar formRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'aria-label'\n      }],\n      name: 'form'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'aria-labelledby'\n      }],\n      name: 'form'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'name'\n      }],\n      name: 'form'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = formRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar genericRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'a'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'area'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'aside'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'b'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'bdo'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'body'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'data'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'div'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      constraints: ['scoped to the main element', 'scoped to a sectioning content element', 'scoped to a sectioning root element other than body'],\n      name: 'footer'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      constraints: ['scoped to the main element', 'scoped to a sectioning content element', 'scoped to a sectioning root element other than body'],\n      name: 'header'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'hgroup'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'i'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'pre'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'q'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'samp'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'section'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'small'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'span'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'u'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = exports.default = genericRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar gridRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-multiselectable': null,\n    'aria-readonly': null\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['row'], ['row', 'rowgroup']],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite'], ['roletype', 'structure', 'section', 'table']]\n};\nvar _default = exports.default = gridRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar gridcellRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null,\n    'aria-readonly': null,\n    'aria-required': null,\n    'aria-selected': null\n  },\n  relatedConcepts: [{\n    concept: {\n      constraints: ['ancestor table element has grid role', 'ancestor table element has treegrid role'],\n      name: 'td'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: ['row'],\n  requiredContextRole: ['row'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'cell'], ['roletype', 'widget']]\n};\nvar _default = exports.default = gridcellRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar groupRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-activedescendant': null,\n    'aria-disabled': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'details'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'fieldset'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'optgroup'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'address'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = groupRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar headingRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-level': '2'\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'h1'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'h2'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'h3'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'h4'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'h5'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'h6'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-level': '2'\n  },\n  superClass: [['roletype', 'structure', 'sectionhead']]\n};\nvar _default = exports.default = headingRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar imgRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'alt'\n      }],\n      name: 'img'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['undefined'],\n        name: 'alt'\n      }],\n      name: 'img'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'imggroup'\n    },\n    module: 'DTB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = imgRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar insertionRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'ins'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = insertionRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar linkRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-expanded': null,\n    'aria-haspopup': null\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'href'\n      }],\n      name: 'a'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'href'\n      }],\n      name: 'area'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'command']]\n};\nvar _default = exports.default = linkRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar listRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'menu'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'ol'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'ul'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['listitem']],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = listRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar listboxRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-invalid': null,\n    'aria-multiselectable': null,\n    'aria-readonly': null,\n    'aria-required': null,\n    'aria-orientation': 'vertical'\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        constraints: ['>1'],\n        name: 'size'\n      }],\n      constraints: ['the size attribute value is greater than 1'],\n      name: 'select'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        name: 'multiple'\n      }],\n      name: 'select'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'datalist'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'list'\n    },\n    module: 'ARIA'\n  }, {\n    concept: {\n      name: 'select'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['option', 'group'], ['option']],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite', 'select'], ['roletype', 'structure', 'section', 'group', 'select']]\n};\nvar _default = exports.default = listboxRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar listitemRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-level': null,\n    'aria-posinset': null,\n    'aria-setsize': null\n  },\n  relatedConcepts: [{\n    concept: {\n      constraints: ['direct descendant of ol', 'direct descendant of ul', 'direct descendant of menu'],\n      name: 'li'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'item'\n    },\n    module: 'XForms'\n  }],\n  requireContextRole: ['directory', 'list'],\n  requiredContextRole: ['directory', 'list'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = listitemRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar logRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-live': 'polite'\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = logRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar mainRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'main'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = mainRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar markRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: [],\n  props: {\n    'aria-braillelabel': null,\n    'aria-brailleroledescription': null,\n    'aria-description': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'mark'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = markRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar marqueeRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = marqueeRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar mathRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'math'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = mathRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar menuRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-orientation': 'vertical'\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'MENU'\n    },\n    module: 'JAPI'\n  }, {\n    concept: {\n      name: 'list'\n    },\n    module: 'ARIA'\n  }, {\n    concept: {\n      name: 'select'\n    },\n    module: 'XForms'\n  }, {\n    concept: {\n      name: 'sidebar'\n    },\n    module: 'DTB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['menuitem', 'group'], ['menuitemradio', 'group'], ['menuitemcheckbox', 'group'], ['menuitem'], ['menuitemcheckbox'], ['menuitemradio']],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite', 'select'], ['roletype', 'structure', 'section', 'group', 'select']]\n};\nvar _default = exports.default = menuRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar menubarRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-orientation': 'horizontal'\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'toolbar'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['menuitem', 'group'], ['menuitemradio', 'group'], ['menuitemcheckbox', 'group'], ['menuitem'], ['menuitemcheckbox'], ['menuitemradio']],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite', 'select', 'menu'], ['roletype', 'structure', 'section', 'group', 'select', 'menu']]\n};\nvar _default = exports.default = menubarRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar menuitemRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-posinset': null,\n    'aria-setsize': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'MENU_ITEM'\n    },\n    module: 'JAP<PERSON>'\n  }, {\n    concept: {\n      name: 'listitem'\n    },\n    module: 'ARIA'\n  }, {\n    concept: {\n      name: 'option'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: ['group', 'menu', 'menubar'],\n  requiredContextRole: ['group', 'menu', 'menubar'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'command']]\n};\nvar _default = exports.default = menuitemRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar menuitemcheckboxRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'menuitem'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: ['group', 'menu', 'menubar'],\n  requiredContextRole: ['group', 'menu', 'menubar'],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-checked': null\n  },\n  superClass: [['roletype', 'widget', 'input', 'checkbox'], ['roletype', 'widget', 'command', 'menuitem']]\n};\nvar _default = exports.default = menuitemcheckboxRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar menuitemradioRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'menuitem'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: ['group', 'menu', 'menubar'],\n  requiredContextRole: ['group', 'menu', 'menubar'],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-checked': null\n  },\n  superClass: [['roletype', 'widget', 'input', 'checkbox', 'menuitemcheckbox'], ['roletype', 'widget', 'command', 'menuitem', 'menuitemcheckbox'], ['roletype', 'widget', 'input', 'radio']]\n};\nvar _default = exports.default = menuitemradioRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar meterRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-valuetext': null,\n    'aria-valuemax': '100',\n    'aria-valuemin': '0'\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'meter'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-valuenow': null\n  },\n  superClass: [['roletype', 'structure', 'range']]\n};\nvar _default = exports.default = meterRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar navigationRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'nav'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = navigationRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar noneRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: [],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: []\n};\nvar _default = exports.default = noneRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar noteRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = noteRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar optionRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-checked': null,\n    'aria-posinset': null,\n    'aria-setsize': null,\n    'aria-selected': 'false'\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'item'\n    },\n    module: 'XForms'\n  }, {\n    concept: {\n      name: 'listitem'\n    },\n    module: 'ARIA'\n  }, {\n    concept: {\n      name: 'option'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-selected': 'false'\n  },\n  superClass: [['roletype', 'widget', 'input']]\n};\nvar _default = exports.default = optionRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar paragraphRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'p'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = paragraphRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar presentationRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'alt',\n        value: ''\n      }],\n      name: 'img'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = exports.default = presentationRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar progressbarRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-valuetext': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'progress'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'status'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'range'], ['roletype', 'widget']]\n};\nvar _default = exports.default = progressbarRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar radioRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-checked': null,\n    'aria-posinset': null,\n    'aria-setsize': null\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'radio'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-checked': null\n  },\n  superClass: [['roletype', 'widget', 'input']]\n};\nvar _default = exports.default = radioRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar radiogroupRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-invalid': null,\n    'aria-readonly': null,\n    'aria-required': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'list'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['radio']],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite', 'select'], ['roletype', 'structure', 'section', 'group', 'select']]\n};\nvar _default = exports.default = radiogroupRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar regionRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'aria-label'\n      }],\n      name: 'section'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['set'],\n        name: 'aria-labelledby'\n      }],\n      name: 'section'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'Device Independence Glossart perceivable unit'\n    }\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = regionRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar rowRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-colindex': null,\n    'aria-expanded': null,\n    'aria-level': null,\n    'aria-posinset': null,\n    'aria-rowindex': null,\n    'aria-selected': null,\n    'aria-setsize': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'tr'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: ['grid', 'rowgroup', 'table', 'treegrid'],\n  requiredContextRole: ['grid', 'rowgroup', 'table', 'treegrid'],\n  requiredOwnedElements: [['cell'], ['columnheader'], ['gridcell'], ['rowheader']],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'group'], ['roletype', 'widget']]\n};\nvar _default = exports.default = rowRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar rowgroupRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'tbody'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'tfoot'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'thead'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: ['grid', 'table', 'treegrid'],\n  requiredContextRole: ['grid', 'table', 'treegrid'],\n  requiredOwnedElements: [['row']],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = exports.default = rowgroupRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar rowheaderRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-sort': null\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'scope',\n        value: 'row'\n      }],\n      name: 'th'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        name: 'scope',\n        value: 'rowgroup'\n      }],\n      name: 'th'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: ['row', 'rowgroup'],\n  requiredContextRole: ['row', 'rowgroup'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'cell'], ['roletype', 'structure', 'section', 'cell', 'gridcell'], ['roletype', 'widget', 'gridcell'], ['roletype', 'structure', 'sectionhead']]\n};\nvar _default = exports.default = rowheaderRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar scrollbarRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-valuetext': null,\n    'aria-orientation': 'vertical',\n    'aria-valuemax': '100',\n    'aria-valuemin': '0'\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-controls': null,\n    'aria-valuenow': null\n  },\n  superClass: [['roletype', 'structure', 'range'], ['roletype', 'widget']]\n};\nvar _default = exports.default = scrollbarRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar searchRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = searchRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar searchboxRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        constraints: ['undefined'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'search'\n      }],\n      constraints: ['the list attribute is not set'],\n      name: 'input'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'input', 'textbox']]\n};\nvar _default = exports.default = searchboxRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar separatorRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-orientation': 'horizontal',\n    'aria-valuemax': '100',\n    'aria-valuemin': '0',\n    'aria-valuenow': null,\n    'aria-valuetext': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'hr'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure']]\n};\nvar _default = exports.default = separatorRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar sliderRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-haspopup': null,\n    'aria-invalid': null,\n    'aria-readonly': null,\n    'aria-valuetext': null,\n    'aria-orientation': 'horizontal',\n    'aria-valuemax': '100',\n    'aria-valuemin': '0'\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'range'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-valuenow': null\n  },\n  superClass: [['roletype', 'widget', 'input'], ['roletype', 'structure', 'range']]\n};\nvar _default = exports.default = sliderRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar spinbuttonRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-invalid': null,\n    'aria-readonly': null,\n    'aria-required': null,\n    'aria-valuetext': null,\n    'aria-valuenow': '0'\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        name: 'type',\n        value: 'number'\n      }],\n      name: 'input'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite'], ['roletype', 'widget', 'input'], ['roletype', 'structure', 'range']]\n};\nvar _default = exports.default = spinbuttonRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar statusRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-atomic': 'true',\n    'aria-live': 'polite'\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'output'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = statusRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar strongRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'strong'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = strongRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar subscriptRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'sub'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = subscriptRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar superscriptRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: ['aria-label', 'aria-labelledby'],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'sup'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = superscriptRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar switchRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'button'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-checked': null\n  },\n  superClass: [['roletype', 'widget', 'input', 'checkbox']]\n};\nvar _default = exports.default = switchRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar tabRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-posinset': null,\n    'aria-setsize': null,\n    'aria-selected': 'false'\n  },\n  relatedConcepts: [],\n  requireContextRole: ['tablist'],\n  requiredContextRole: ['tablist'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'sectionhead'], ['roletype', 'widget']]\n};\nvar _default = exports.default = tabRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar tableRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-colcount': null,\n    'aria-rowcount': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'table'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['row'], ['row', 'rowgroup']],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = tableRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar tablistRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-level': null,\n    'aria-multiselectable': null,\n    'aria-orientation': 'horizontal'\n  },\n  relatedConcepts: [{\n    module: 'DAISY',\n    concept: {\n      name: 'guide'\n    }\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['tab']],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite']]\n};\nvar _default = exports.default = tablistRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar tabpanelRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = tabpanelRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar termRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'dfn'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'dt'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = termRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar textboxRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-activedescendant': null,\n    'aria-autocomplete': null,\n    'aria-errormessage': null,\n    'aria-haspopup': null,\n    'aria-invalid': null,\n    'aria-multiline': null,\n    'aria-placeholder': null,\n    'aria-readonly': null,\n    'aria-required': null\n  },\n  relatedConcepts: [{\n    concept: {\n      attributes: [{\n        constraints: ['undefined'],\n        name: 'type'\n      }, {\n        constraints: ['undefined'],\n        name: 'list'\n      }],\n      constraints: ['the list attribute is not set'],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['undefined'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'email'\n      }],\n      constraints: ['the list attribute is not set'],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['undefined'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'tel'\n      }],\n      constraints: ['the list attribute is not set'],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['undefined'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'text'\n      }],\n      constraints: ['the list attribute is not set'],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      attributes: [{\n        constraints: ['undefined'],\n        name: 'list'\n      }, {\n        name: 'type',\n        value: 'url'\n      }],\n      constraints: ['the list attribute is not set'],\n      name: 'input'\n    },\n    module: 'HTML'\n  }, {\n    concept: {\n      name: 'input'\n    },\n    module: 'XForms'\n  }, {\n    concept: {\n      name: 'textarea'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'input']]\n};\nvar _default = exports.default = textboxRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar timeRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'time'\n    },\n    module: 'HTML'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = timeRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar timerRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'status']]\n};\nvar _default = exports.default = timerRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar toolbarRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-orientation': 'horizontal'\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'menubar'\n    },\n    module: 'ARIA'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'group']]\n};\nvar _default = exports.default = toolbarRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar tooltipRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = tooltipRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar treeRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-invalid': null,\n    'aria-multiselectable': null,\n    'aria-required': null,\n    'aria-orientation': 'vertical'\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['treeitem', 'group'], ['treeitem']],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite', 'select'], ['roletype', 'structure', 'section', 'group', 'select']]\n};\nvar _default = exports.default = treeRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar treegridRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['row'], ['row', 'rowgroup']],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'composite', 'grid'], ['roletype', 'structure', 'section', 'table', 'grid'], ['roletype', 'widget', 'composite', 'select', 'tree'], ['roletype', 'structure', 'section', 'group', 'select', 'tree']]\n};\nvar _default = exports.default = treegridRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar treeitemRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-expanded': null,\n    'aria-haspopup': null\n  },\n  relatedConcepts: [],\n  requireContextRole: ['group', 'tree'],\n  requiredContextRole: ['group', 'tree'],\n  requiredOwnedElements: [],\n  requiredProps: {\n    'aria-selected': null\n  },\n  superClass: [['roletype', 'structure', 'section', 'listitem'], ['roletype', 'widget', 'input', 'option']]\n};\nvar _default = exports.default = treeitemRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _alertRole = _interopRequireDefault(require(\"./literal/alertRole\"));\nvar _alertdialogRole = _interopRequireDefault(require(\"./literal/alertdialogRole\"));\nvar _applicationRole = _interopRequireDefault(require(\"./literal/applicationRole\"));\nvar _articleRole = _interopRequireDefault(require(\"./literal/articleRole\"));\nvar _bannerRole = _interopRequireDefault(require(\"./literal/bannerRole\"));\nvar _blockquoteRole = _interopRequireDefault(require(\"./literal/blockquoteRole\"));\nvar _buttonRole = _interopRequireDefault(require(\"./literal/buttonRole\"));\nvar _captionRole = _interopRequireDefault(require(\"./literal/captionRole\"));\nvar _cellRole = _interopRequireDefault(require(\"./literal/cellRole\"));\nvar _checkboxRole = _interopRequireDefault(require(\"./literal/checkboxRole\"));\nvar _codeRole = _interopRequireDefault(require(\"./literal/codeRole\"));\nvar _columnheaderRole = _interopRequireDefault(require(\"./literal/columnheaderRole\"));\nvar _comboboxRole = _interopRequireDefault(require(\"./literal/comboboxRole\"));\nvar _complementaryRole = _interopRequireDefault(require(\"./literal/complementaryRole\"));\nvar _contentinfoRole = _interopRequireDefault(require(\"./literal/contentinfoRole\"));\nvar _definitionRole = _interopRequireDefault(require(\"./literal/definitionRole\"));\nvar _deletionRole = _interopRequireDefault(require(\"./literal/deletionRole\"));\nvar _dialogRole = _interopRequireDefault(require(\"./literal/dialogRole\"));\nvar _directoryRole = _interopRequireDefault(require(\"./literal/directoryRole\"));\nvar _documentRole = _interopRequireDefault(require(\"./literal/documentRole\"));\nvar _emphasisRole = _interopRequireDefault(require(\"./literal/emphasisRole\"));\nvar _feedRole = _interopRequireDefault(require(\"./literal/feedRole\"));\nvar _figureRole = _interopRequireDefault(require(\"./literal/figureRole\"));\nvar _formRole = _interopRequireDefault(require(\"./literal/formRole\"));\nvar _genericRole = _interopRequireDefault(require(\"./literal/genericRole\"));\nvar _gridRole = _interopRequireDefault(require(\"./literal/gridRole\"));\nvar _gridcellRole = _interopRequireDefault(require(\"./literal/gridcellRole\"));\nvar _groupRole = _interopRequireDefault(require(\"./literal/groupRole\"));\nvar _headingRole = _interopRequireDefault(require(\"./literal/headingRole\"));\nvar _imgRole = _interopRequireDefault(require(\"./literal/imgRole\"));\nvar _insertionRole = _interopRequireDefault(require(\"./literal/insertionRole\"));\nvar _linkRole = _interopRequireDefault(require(\"./literal/linkRole\"));\nvar _listRole = _interopRequireDefault(require(\"./literal/listRole\"));\nvar _listboxRole = _interopRequireDefault(require(\"./literal/listboxRole\"));\nvar _listitemRole = _interopRequireDefault(require(\"./literal/listitemRole\"));\nvar _logRole = _interopRequireDefault(require(\"./literal/logRole\"));\nvar _mainRole = _interopRequireDefault(require(\"./literal/mainRole\"));\nvar _markRole = _interopRequireDefault(require(\"./literal/markRole\"));\nvar _marqueeRole = _interopRequireDefault(require(\"./literal/marqueeRole\"));\nvar _mathRole = _interopRequireDefault(require(\"./literal/mathRole\"));\nvar _menuRole = _interopRequireDefault(require(\"./literal/menuRole\"));\nvar _menubarRole = _interopRequireDefault(require(\"./literal/menubarRole\"));\nvar _menuitemRole = _interopRequireDefault(require(\"./literal/menuitemRole\"));\nvar _menuitemcheckboxRole = _interopRequireDefault(require(\"./literal/menuitemcheckboxRole\"));\nvar _menuitemradioRole = _interopRequireDefault(require(\"./literal/menuitemradioRole\"));\nvar _meterRole = _interopRequireDefault(require(\"./literal/meterRole\"));\nvar _navigationRole = _interopRequireDefault(require(\"./literal/navigationRole\"));\nvar _noneRole = _interopRequireDefault(require(\"./literal/noneRole\"));\nvar _noteRole = _interopRequireDefault(require(\"./literal/noteRole\"));\nvar _optionRole = _interopRequireDefault(require(\"./literal/optionRole\"));\nvar _paragraphRole = _interopRequireDefault(require(\"./literal/paragraphRole\"));\nvar _presentationRole = _interopRequireDefault(require(\"./literal/presentationRole\"));\nvar _progressbarRole = _interopRequireDefault(require(\"./literal/progressbarRole\"));\nvar _radioRole = _interopRequireDefault(require(\"./literal/radioRole\"));\nvar _radiogroupRole = _interopRequireDefault(require(\"./literal/radiogroupRole\"));\nvar _regionRole = _interopRequireDefault(require(\"./literal/regionRole\"));\nvar _rowRole = _interopRequireDefault(require(\"./literal/rowRole\"));\nvar _rowgroupRole = _interopRequireDefault(require(\"./literal/rowgroupRole\"));\nvar _rowheaderRole = _interopRequireDefault(require(\"./literal/rowheaderRole\"));\nvar _scrollbarRole = _interopRequireDefault(require(\"./literal/scrollbarRole\"));\nvar _searchRole = _interopRequireDefault(require(\"./literal/searchRole\"));\nvar _searchboxRole = _interopRequireDefault(require(\"./literal/searchboxRole\"));\nvar _separatorRole = _interopRequireDefault(require(\"./literal/separatorRole\"));\nvar _sliderRole = _interopRequireDefault(require(\"./literal/sliderRole\"));\nvar _spinbuttonRole = _interopRequireDefault(require(\"./literal/spinbuttonRole\"));\nvar _statusRole = _interopRequireDefault(require(\"./literal/statusRole\"));\nvar _strongRole = _interopRequireDefault(require(\"./literal/strongRole\"));\nvar _subscriptRole = _interopRequireDefault(require(\"./literal/subscriptRole\"));\nvar _superscriptRole = _interopRequireDefault(require(\"./literal/superscriptRole\"));\nvar _switchRole = _interopRequireDefault(require(\"./literal/switchRole\"));\nvar _tabRole = _interopRequireDefault(require(\"./literal/tabRole\"));\nvar _tableRole = _interopRequireDefault(require(\"./literal/tableRole\"));\nvar _tablistRole = _interopRequireDefault(require(\"./literal/tablistRole\"));\nvar _tabpanelRole = _interopRequireDefault(require(\"./literal/tabpanelRole\"));\nvar _termRole = _interopRequireDefault(require(\"./literal/termRole\"));\nvar _textboxRole = _interopRequireDefault(require(\"./literal/textboxRole\"));\nvar _timeRole = _interopRequireDefault(require(\"./literal/timeRole\"));\nvar _timerRole = _interopRequireDefault(require(\"./literal/timerRole\"));\nvar _toolbarRole = _interopRequireDefault(require(\"./literal/toolbarRole\"));\nvar _tooltipRole = _interopRequireDefault(require(\"./literal/tooltipRole\"));\nvar _treeRole = _interopRequireDefault(require(\"./literal/treeRole\"));\nvar _treegridRole = _interopRequireDefault(require(\"./literal/treegridRole\"));\nvar _treeitemRole = _interopRequireDefault(require(\"./literal/treeitemRole\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nvar ariaLiteralRoles = [['alert', _alertRole.default], ['alertdialog', _alertdialogRole.default], ['application', _applicationRole.default], ['article', _articleRole.default], ['banner', _bannerRole.default], ['blockquote', _blockquoteRole.default], ['button', _buttonRole.default], ['caption', _captionRole.default], ['cell', _cellRole.default], ['checkbox', _checkboxRole.default], ['code', _codeRole.default], ['columnheader', _columnheaderRole.default], ['combobox', _comboboxRole.default], ['complementary', _complementaryRole.default], ['contentinfo', _contentinfoRole.default], ['definition', _definitionRole.default], ['deletion', _deletionRole.default], ['dialog', _dialogRole.default], ['directory', _directoryRole.default], ['document', _documentRole.default], ['emphasis', _emphasisRole.default], ['feed', _feedRole.default], ['figure', _figureRole.default], ['form', _formRole.default], ['generic', _genericRole.default], ['grid', _gridRole.default], ['gridcell', _gridcellRole.default], ['group', _groupRole.default], ['heading', _headingRole.default], ['img', _imgRole.default], ['insertion', _insertionRole.default], ['link', _linkRole.default], ['list', _listRole.default], ['listbox', _listboxRole.default], ['listitem', _listitemRole.default], ['log', _logRole.default], ['main', _mainRole.default], ['mark', _markRole.default], ['marquee', _marqueeRole.default], ['math', _mathRole.default], ['menu', _menuRole.default], ['menubar', _menubarRole.default], ['menuitem', _menuitemRole.default], ['menuitemcheckbox', _menuitemcheckboxRole.default], ['menuitemradio', _menuitemradioRole.default], ['meter', _meterRole.default], ['navigation', _navigationRole.default], ['none', _noneRole.default], ['note', _noteRole.default], ['option', _optionRole.default], ['paragraph', _paragraphRole.default], ['presentation', _presentationRole.default], ['progressbar', _progressbarRole.default], ['radio', _radioRole.default], ['radiogroup', _radiogroupRole.default], ['region', _regionRole.default], ['row', _rowRole.default], ['rowgroup', _rowgroupRole.default], ['rowheader', _rowheaderRole.default], ['scrollbar', _scrollbarRole.default], ['search', _searchRole.default], ['searchbox', _searchboxRole.default], ['separator', _separatorRole.default], ['slider', _sliderRole.default], ['spinbutton', _spinbuttonRole.default], ['status', _statusRole.default], ['strong', _strongRole.default], ['subscript', _subscriptRole.default], ['superscript', _superscriptRole.default], ['switch', _switchRole.default], ['tab', _tabRole.default], ['table', _tableRole.default], ['tablist', _tablistRole.default], ['tabpanel', _tabpanelRole.default], ['term', _termRole.default], ['textbox', _textboxRole.default], ['time', _timeRole.default], ['timer', _timerRole.default], ['toolbar', _toolbarRole.default], ['tooltip', _tooltipRole.default], ['tree', _treeRole.default], ['treegrid', _treegridRole.default], ['treeitem', _treeitemRole.default]];\nvar _default = exports.default = ariaLiteralRoles;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docAbstractRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'abstract [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = docAbstractRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docAcknowledgmentsRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'acknowledgments [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docAcknowledgmentsRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docAfterwordRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'afterword [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docAfterwordRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docAppendixRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'appendix [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docAppendixRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docBacklinkRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'referrer [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'command', 'link']]\n};\nvar _default = exports.default = docBacklinkRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docBiblioentryRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'EPUB biblioentry [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: ['doc-bibliography'],\n  requiredContextRole: ['doc-bibliography'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'listitem']]\n};\nvar _default = exports.default = docBiblioentryRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docBibliographyRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'bibliography [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['doc-biblioentry']],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docBibliographyRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docBibliorefRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'biblioref [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'command', 'link']]\n};\nvar _default = exports.default = docBibliorefRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docChapterRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'chapter [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docChapterRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docColophonRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'colophon [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = docColophonRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docConclusionRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'conclusion [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docConclusionRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docCoverRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'cover [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'img']]\n};\nvar _default = exports.default = docCoverRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docCreditRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'credit [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = docCreditRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docCreditsRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'credits [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docCreditsRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docDedicationRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'dedication [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = docDedicationRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docEndnoteRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'rearnote [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: ['doc-endnotes'],\n  requiredContextRole: ['doc-endnotes'],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'listitem']]\n};\nvar _default = exports.default = docEndnoteRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docEndnotesRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'rearnotes [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['doc-endnote']],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docEndnotesRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docEpigraphRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'epigraph [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = docEpigraphRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docEpilogueRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'epilogue [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docEpilogueRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docErrataRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'errata [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docErrataRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docExampleRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = docExampleRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docFootnoteRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'footnote [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = docFootnoteRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docForewordRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'foreword [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docForewordRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docGlossaryRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'glossary [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [['definition'], ['term']],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docGlossaryRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docGlossrefRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'glossref [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'command', 'link']]\n};\nvar _default = exports.default = docGlossrefRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docIndexRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'index [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark', 'navigation']]\n};\nvar _default = exports.default = docIndexRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docIntroductionRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'introduction [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docIntroductionRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docNoterefRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'noteref [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'widget', 'command', 'link']]\n};\nvar _default = exports.default = docNoterefRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docNoticeRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'notice [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'note']]\n};\nvar _default = exports.default = docNoticeRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docPagebreakRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'pagebreak [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'separator']]\n};\nvar _default = exports.default = docPagebreakRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docPagefooterRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: [],\n  props: {\n    'aria-braillelabel': null,\n    'aria-brailleroledescription': null,\n    'aria-description': null,\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = docPagefooterRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docPageheaderRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['prohibited'],\n  prohibitedProps: [],\n  props: {\n    'aria-braillelabel': null,\n    'aria-brailleroledescription': null,\n    'aria-description': null,\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = docPageheaderRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docPagelistRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'page-list [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark', 'navigation']]\n};\nvar _default = exports.default = docPagelistRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docPartRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'part [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docPartRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docPrefaceRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'preface [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docPrefaceRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docPrologueRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'prologue [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark']]\n};\nvar _default = exports.default = docPrologueRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docPullquoteRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {},\n  relatedConcepts: [{\n    concept: {\n      name: 'pullquote [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['none']]\n};\nvar _default = exports.default = docPullquoteRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docQnaRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'qna [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section']]\n};\nvar _default = exports.default = docQnaRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docSubtitleRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'subtitle [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'sectionhead']]\n};\nvar _default = exports.default = docSubtitleRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docTipRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'help [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'note']]\n};\nvar _default = exports.default = docTipRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar docTocRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    concept: {\n      name: 'toc [EPUB-SSV]'\n    },\n    module: 'EPUB'\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'landmark', 'navigation']]\n};\nvar _default = exports.default = docTocRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _docAbstractRole = _interopRequireDefault(require(\"./dpub/docAbstractRole\"));\nvar _docAcknowledgmentsRole = _interopRequireDefault(require(\"./dpub/docAcknowledgmentsRole\"));\nvar _docAfterwordRole = _interopRequireDefault(require(\"./dpub/docAfterwordRole\"));\nvar _docAppendixRole = _interopRequireDefault(require(\"./dpub/docAppendixRole\"));\nvar _docBacklinkRole = _interopRequireDefault(require(\"./dpub/docBacklinkRole\"));\nvar _docBiblioentryRole = _interopRequireDefault(require(\"./dpub/docBiblioentryRole\"));\nvar _docBibliographyRole = _interopRequireDefault(require(\"./dpub/docBibliographyRole\"));\nvar _docBibliorefRole = _interopRequireDefault(require(\"./dpub/docBibliorefRole\"));\nvar _docChapterRole = _interopRequireDefault(require(\"./dpub/docChapterRole\"));\nvar _docColophonRole = _interopRequireDefault(require(\"./dpub/docColophonRole\"));\nvar _docConclusionRole = _interopRequireDefault(require(\"./dpub/docConclusionRole\"));\nvar _docCoverRole = _interopRequireDefault(require(\"./dpub/docCoverRole\"));\nvar _docCreditRole = _interopRequireDefault(require(\"./dpub/docCreditRole\"));\nvar _docCreditsRole = _interopRequireDefault(require(\"./dpub/docCreditsRole\"));\nvar _docDedicationRole = _interopRequireDefault(require(\"./dpub/docDedicationRole\"));\nvar _docEndnoteRole = _interopRequireDefault(require(\"./dpub/docEndnoteRole\"));\nvar _docEndnotesRole = _interopRequireDefault(require(\"./dpub/docEndnotesRole\"));\nvar _docEpigraphRole = _interopRequireDefault(require(\"./dpub/docEpigraphRole\"));\nvar _docEpilogueRole = _interopRequireDefault(require(\"./dpub/docEpilogueRole\"));\nvar _docErrataRole = _interopRequireDefault(require(\"./dpub/docErrataRole\"));\nvar _docExampleRole = _interopRequireDefault(require(\"./dpub/docExampleRole\"));\nvar _docFootnoteRole = _interopRequireDefault(require(\"./dpub/docFootnoteRole\"));\nvar _docForewordRole = _interopRequireDefault(require(\"./dpub/docForewordRole\"));\nvar _docGlossaryRole = _interopRequireDefault(require(\"./dpub/docGlossaryRole\"));\nvar _docGlossrefRole = _interopRequireDefault(require(\"./dpub/docGlossrefRole\"));\nvar _docIndexRole = _interopRequireDefault(require(\"./dpub/docIndexRole\"));\nvar _docIntroductionRole = _interopRequireDefault(require(\"./dpub/docIntroductionRole\"));\nvar _docNoterefRole = _interopRequireDefault(require(\"./dpub/docNoterefRole\"));\nvar _docNoticeRole = _interopRequireDefault(require(\"./dpub/docNoticeRole\"));\nvar _docPagebreakRole = _interopRequireDefault(require(\"./dpub/docPagebreakRole\"));\nvar _docPagefooterRole = _interopRequireDefault(require(\"./dpub/docPagefooterRole\"));\nvar _docPageheaderRole = _interopRequireDefault(require(\"./dpub/docPageheaderRole\"));\nvar _docPagelistRole = _interopRequireDefault(require(\"./dpub/docPagelistRole\"));\nvar _docPartRole = _interopRequireDefault(require(\"./dpub/docPartRole\"));\nvar _docPrefaceRole = _interopRequireDefault(require(\"./dpub/docPrefaceRole\"));\nvar _docPrologueRole = _interopRequireDefault(require(\"./dpub/docPrologueRole\"));\nvar _docPullquoteRole = _interopRequireDefault(require(\"./dpub/docPullquoteRole\"));\nvar _docQnaRole = _interopRequireDefault(require(\"./dpub/docQnaRole\"));\nvar _docSubtitleRole = _interopRequireDefault(require(\"./dpub/docSubtitleRole\"));\nvar _docTipRole = _interopRequireDefault(require(\"./dpub/docTipRole\"));\nvar _docTocRole = _interopRequireDefault(require(\"./dpub/docTocRole\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nvar ariaDpubRoles = [['doc-abstract', _docAbstractRole.default], ['doc-acknowledgments', _docAcknowledgmentsRole.default], ['doc-afterword', _docAfterwordRole.default], ['doc-appendix', _docAppendixRole.default], ['doc-backlink', _docBacklinkRole.default], ['doc-biblioentry', _docBiblioentryRole.default], ['doc-bibliography', _docBibliographyRole.default], ['doc-biblioref', _docBibliorefRole.default], ['doc-chapter', _docChapterRole.default], ['doc-colophon', _docColophonRole.default], ['doc-conclusion', _docConclusionRole.default], ['doc-cover', _docCoverRole.default], ['doc-credit', _docCreditRole.default], ['doc-credits', _docCreditsRole.default], ['doc-dedication', _docDedicationRole.default], ['doc-endnote', _docEndnoteRole.default], ['doc-endnotes', _docEndnotesRole.default], ['doc-epigraph', _docEpigraphRole.default], ['doc-epilogue', _docEpilogueRole.default], ['doc-errata', _docErrataRole.default], ['doc-example', _docExampleRole.default], ['doc-footnote', _docFootnoteRole.default], ['doc-foreword', _docForewordRole.default], ['doc-glossary', _docGlossaryRole.default], ['doc-glossref', _docGlossrefRole.default], ['doc-index', _docIndexRole.default], ['doc-introduction', _docIntroductionRole.default], ['doc-noteref', _docNoterefRole.default], ['doc-notice', _docNoticeRole.default], ['doc-pagebreak', _docPagebreakRole.default], ['doc-pagefooter', _docPagefooterRole.default], ['doc-pageheader', _docPageheaderRole.default], ['doc-pagelist', _docPagelistRole.default], ['doc-part', _docPartRole.default], ['doc-preface', _docPrefaceRole.default], ['doc-prologue', _docPrologueRole.default], ['doc-pullquote', _docPullquoteRole.default], ['doc-qna', _docQnaRole.default], ['doc-subtitle', _docSubtitleRole.default], ['doc-tip', _docTipRole.default], ['doc-toc', _docTocRole.default]];\nvar _default = exports.default = ariaDpubRoles;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar graphicsDocumentRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    module: 'GRAPHICS',\n    concept: {\n      name: 'graphics-object'\n    }\n  }, {\n    module: 'ARIA',\n    concept: {\n      name: 'img'\n    }\n  }, {\n    module: 'ARIA',\n    concept: {\n      name: 'article'\n    }\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'document']]\n};\nvar _default = exports.default = graphicsDocumentRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar graphicsObjectRole = {\n  abstract: false,\n  accessibleNameRequired: false,\n  baseConcepts: [],\n  childrenPresentational: false,\n  nameFrom: ['author', 'contents'],\n  prohibitedProps: [],\n  props: {\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [{\n    module: 'GRAPHICS',\n    concept: {\n      name: 'graphics-document'\n    }\n  }, {\n    module: 'ARIA',\n    concept: {\n      name: 'group'\n    }\n  }, {\n    module: 'ARIA',\n    concept: {\n      name: 'img'\n    }\n  }, {\n    module: 'GRAPHICS',\n    concept: {\n      name: 'graphics-symbol'\n    }\n  }],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'group']]\n};\nvar _default = exports.default = graphicsObjectRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar graphicsSymbolRole = {\n  abstract: false,\n  accessibleNameRequired: true,\n  baseConcepts: [],\n  childrenPresentational: true,\n  nameFrom: ['author'],\n  prohibitedProps: [],\n  props: {\n    'aria-disabled': null,\n    'aria-errormessage': null,\n    'aria-expanded': null,\n    'aria-haspopup': null,\n    'aria-invalid': null\n  },\n  relatedConcepts: [],\n  requireContextRole: [],\n  requiredContextRole: [],\n  requiredOwnedElements: [],\n  requiredProps: {},\n  superClass: [['roletype', 'structure', 'section', 'img']]\n};\nvar _default = exports.default = graphicsSymbolRole;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _graphicsDocumentRole = _interopRequireDefault(require(\"./graphics/graphicsDocumentRole\"));\nvar _graphicsObjectRole = _interopRequireDefault(require(\"./graphics/graphicsObjectRole\"));\nvar _graphicsSymbolRole = _interopRequireDefault(require(\"./graphics/graphicsSymbolRole\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nvar ariaGraphicsRoles = [['graphics-document', _graphicsDocumentRole.default], ['graphics-object', _graphicsObjectRole.default], ['graphics-symbol', _graphicsSymbolRole.default]];\nvar _default = exports.default = ariaGraphicsRoles;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _ariaAbstractRoles = _interopRequireDefault(require(\"./etc/roles/ariaAbstractRoles\"));\nvar _ariaLiteralRoles = _interopRequireDefault(require(\"./etc/roles/ariaLiteralRoles\"));\nvar _ariaDpubRoles = _interopRequireDefault(require(\"./etc/roles/ariaDpubRoles\"));\nvar _ariaGraphicsRoles = _interopRequireDefault(require(\"./etc/roles/ariaGraphicsRoles\"));\nvar _iterationDecorator = _interopRequireDefault(require(\"./util/iterationDecorator\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nvar roles = [].concat(_ariaAbstractRoles.default, _ariaLiteralRoles.default, _ariaDpubRoles.default, _ariaGraphicsRoles.default);\nroles.forEach(function (_ref) {\n  var _ref2 = _slicedToArray(_ref, 2),\n    roleDefinition = _ref2[1];\n  // Conglomerate the properties\n  var _iterator = _createForOfIteratorHelper(roleDefinition.superClass),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var superClassIter = _step.value;\n      var _iterator2 = _createForOfIteratorHelper(superClassIter),\n        _step2;\n      try {\n        var _loop = function _loop() {\n          var superClassName = _step2.value;\n          var superClassRoleTuple = roles.filter(function (_ref3) {\n            var _ref4 = _slicedToArray(_ref3, 1),\n              name = _ref4[0];\n            return name === superClassName;\n          })[0];\n          if (superClassRoleTuple) {\n            var superClassDefinition = superClassRoleTuple[1];\n            for (var _i = 0, _Object$keys = Object.keys(superClassDefinition.props); _i < _Object$keys.length; _i++) {\n              var prop = _Object$keys[_i];\n              if (\n              // $FlowIssue Accessing the hasOwnProperty on the Object prototype is fine.\n              !Object.prototype.hasOwnProperty.call(roleDefinition.props, prop)) {\n                // $FlowIgnore assigning without an index signature is fine\n                roleDefinition.props[prop] = superClassDefinition.props[prop];\n              }\n            }\n          }\n        };\n        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n          _loop();\n        }\n      } catch (err) {\n        _iterator2.e(err);\n      } finally {\n        _iterator2.f();\n      }\n    }\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n});\nvar rolesMap = {\n  entries: function entries() {\n    return roles;\n  },\n  forEach: function forEach(fn) {\n    var thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    var _iterator3 = _createForOfIteratorHelper(roles),\n      _step3;\n    try {\n      for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n        var _step3$value = _slicedToArray(_step3.value, 2),\n          key = _step3$value[0],\n          values = _step3$value[1];\n        fn.call(thisArg, values, key, roles);\n      }\n    } catch (err) {\n      _iterator3.e(err);\n    } finally {\n      _iterator3.f();\n    }\n  },\n  get: function get(key) {\n    var item = roles.filter(function (tuple) {\n      return tuple[0] === key ? true : false;\n    })[0];\n    return item && item[1];\n  },\n  has: function has(key) {\n    return !!rolesMap.get(key);\n  },\n  keys: function keys() {\n    return roles.map(function (_ref5) {\n      var _ref6 = _slicedToArray(_ref5, 1),\n        key = _ref6[0];\n      return key;\n    });\n  },\n  values: function values() {\n    return roles.map(function (_ref7) {\n      var _ref8 = _slicedToArray(_ref7, 2),\n        values = _ref8[1];\n      return values;\n    });\n  }\n};\nvar _default = exports.default = (0, _iterationDecorator.default)(rolesMap, rolesMap.entries());", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _iterationDecorator = _interopRequireDefault(require(\"./util/iterationDecorator\"));\nvar _rolesMap = _interopRequireDefault(require(\"./rolesMap\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nvar elementRoles = [];\nvar keys = _rolesMap.default.keys();\nfor (var i = 0; i < keys.length; i++) {\n  var key = keys[i];\n  var role = _rolesMap.default.get(key);\n  if (role) {\n    var concepts = [].concat(role.baseConcepts, role.relatedConcepts);\n    var _loop = function _loop() {\n      var relation = concepts[k];\n      if (relation.module === 'HTML') {\n        var concept = relation.concept;\n        if (concept) {\n          var elementRoleRelation = elementRoles.filter(function (relation) {\n            return ariaRoleRelationConceptEquals(relation[0], concept);\n          })[0];\n          var roles;\n          if (elementRoleRelation) {\n            roles = elementRoleRelation[1];\n          } else {\n            roles = [];\n          }\n          var isUnique = true;\n          for (var _i = 0; _i < roles.length; _i++) {\n            if (roles[_i] === key) {\n              isUnique = false;\n              break;\n            }\n          }\n          if (isUnique) {\n            roles.push(key);\n          }\n          if (!elementRoleRelation) {\n            elementRoles.push([concept, roles]);\n          }\n        }\n      }\n    };\n    for (var k = 0; k < concepts.length; k++) {\n      _loop();\n    }\n  }\n}\nvar elementRoleMap = {\n  entries: function entries() {\n    return elementRoles;\n  },\n  forEach: function forEach(fn) {\n    var thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    for (var _i2 = 0, _elementRoles = elementRoles; _i2 < _elementRoles.length; _i2++) {\n      var _elementRoles$_i = _slicedToArray(_elementRoles[_i2], 2),\n        _key = _elementRoles$_i[0],\n        values = _elementRoles$_i[1];\n      fn.call(thisArg, values, _key, elementRoles);\n    }\n  },\n  get: function get(key) {\n    var item = elementRoles.filter(function (tuple) {\n      return key.name === tuple[0].name && ariaRoleRelationConceptAttributeEquals(key.attributes, tuple[0].attributes);\n    })[0];\n    return item && item[1];\n  },\n  has: function has(key) {\n    return !!elementRoleMap.get(key);\n  },\n  keys: function keys() {\n    return elementRoles.map(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 1),\n        key = _ref2[0];\n      return key;\n    });\n  },\n  values: function values() {\n    return elementRoles.map(function (_ref3) {\n      var _ref4 = _slicedToArray(_ref3, 2),\n        values = _ref4[1];\n      return values;\n    });\n  }\n};\nfunction ariaRoleRelationConceptEquals(a, b) {\n  return a.name === b.name && ariaRoleRelationConstraintsEquals(a.constraints, b.constraints) && ariaRoleRelationConceptAttributeEquals(a.attributes, b.attributes);\n}\nfunction ariaRoleRelationConstraintsEquals(a, b) {\n  if (a === undefined && b !== undefined) {\n    return false;\n  }\n  if (a !== undefined && b === undefined) {\n    return false;\n  }\n  if (a !== undefined && b !== undefined) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (var _i3 = 0; _i3 < a.length; _i3++) {\n      if (a[_i3] !== b[_i3]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nfunction ariaRoleRelationConceptAttributeEquals(a, b) {\n  if (a === undefined && b !== undefined) {\n    return false;\n  }\n  if (a !== undefined && b === undefined) {\n    return false;\n  }\n  if (a !== undefined && b !== undefined) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (var _i4 = 0; _i4 < a.length; _i4++) {\n      if (a[_i4].name !== b[_i4].name || a[_i4].value !== b[_i4].value) {\n        return false;\n      }\n      if (a[_i4].constraints === undefined && b[_i4].constraints !== undefined) {\n        return false;\n      }\n      if (a[_i4].constraints !== undefined && b[_i4].constraints === undefined) {\n        return false;\n      }\n      if (a[_i4].constraints !== undefined && b[_i4].constraints !== undefined) {\n        if (a[_i4].constraints.length !== b[_i4].constraints.length) {\n          return false;\n        }\n        for (var j = 0; j < a[_i4].constraints.length; j++) {\n          if (a[_i4].constraints[j] !== b[_i4].constraints[j]) {\n            return false;\n          }\n        }\n      }\n    }\n  }\n  return true;\n}\nvar _default = exports.default = (0, _iterationDecorator.default)(elementRoleMap, elementRoleMap.entries());", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _iterationDecorator = _interopRequireDefault(require(\"./util/iterationDecorator\"));\nvar _rolesMap = _interopRequireDefault(require(\"./rolesMap\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nvar roleElement = [];\nvar keys = _rolesMap.default.keys();\nfor (var i = 0; i < keys.length; i++) {\n  var key = keys[i];\n  var role = _rolesMap.default.get(key);\n  var relationConcepts = [];\n  if (role) {\n    var concepts = [].concat(role.baseConcepts, role.relatedConcepts);\n    for (var k = 0; k < concepts.length; k++) {\n      var relation = concepts[k];\n      if (relation.module === 'HTML') {\n        var concept = relation.concept;\n        if (concept != null) {\n          relationConcepts.push(concept);\n        }\n      }\n    }\n    if (relationConcepts.length > 0) {\n      roleElement.push([key, relationConcepts]);\n    }\n  }\n}\nvar roleElementMap = {\n  entries: function entries() {\n    return roleElement;\n  },\n  forEach: function forEach(fn) {\n    var thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    for (var _i = 0, _roleElement = roleElement; _i < _roleElement.length; _i++) {\n      var _roleElement$_i = _slicedToArray(_roleElement[_i], 2),\n        _key = _roleElement$_i[0],\n        values = _roleElement$_i[1];\n      fn.call(thisArg, values, _key, roleElement);\n    }\n  },\n  get: function get(key) {\n    var item = roleElement.filter(function (tuple) {\n      return tuple[0] === key ? true : false;\n    })[0];\n    return item && item[1];\n  },\n  has: function has(key) {\n    return !!roleElementMap.get(key);\n  },\n  keys: function keys() {\n    return roleElement.map(function (_ref) {\n      var _ref2 = _slicedToArray(_ref, 1),\n        key = _ref2[0];\n      return key;\n    });\n  },\n  values: function values() {\n    return roleElement.map(function (_ref3) {\n      var _ref4 = _slicedToArray(_ref3, 2),\n        values = _ref4[1];\n      return values;\n    });\n  }\n};\nvar _default = exports.default = (0, _iterationDecorator.default)(roleElementMap, roleElementMap.entries());", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.roles = exports.roleElements = exports.elementRoles = exports.dom = exports.aria = void 0;\nvar _ariaPropsMap = _interopRequireDefault(require(\"./ariaPropsMap\"));\nvar _domMap = _interopRequireDefault(require(\"./domMap\"));\nvar _rolesMap = _interopRequireDefault(require(\"./rolesMap\"));\nvar _elementRoleMap = _interopRequireDefault(require(\"./elementRoleMap\"));\nvar _roleElementMap = _interopRequireDefault(require(\"./roleElementMap\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nvar aria = exports.aria = _ariaPropsMap.default;\nvar dom = exports.dom = _domMap.default;\nvar roles = exports.roles = _rolesMap.default;\nvar elementRoles = exports.elementRoles = _elementRoleMap.default;\nvar roleElements = exports.roleElements = _roleElementMap.default;"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,aAAS,gBAAgB;AACvB,UAAI,SAAS;AACb,UAAI,QAAQ;AACZ,UAAI,OAAO;AAAA,QACT,cAAc,SAAS,WAAW;AAChC,iBAAO;AAAA,QACT;AAAA,QACA,MAAM,SAAS,OAAO;AACpB,cAAI,QAAQ,OAAO,QAAQ;AACzB,gBAAI,QAAQ,OAAO,KAAK;AACxB,oBAAQ,QAAQ;AAChB,mBAAO;AAAA,cACL,MAAM;AAAA,cACN;AAAA,YACF;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,cACL,MAAM;AAAA,YACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC/BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB,uBAAuB,uBAA0B;AACtE,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,QAAQ,GAAG;AAAE;AAA2B,aAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,IAAG;AAAE,eAAO,OAAOA;AAAA,MAAG,IAAI,SAAUA,IAAG;AAAE,eAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,MAAG,GAAG,QAAQ,CAAC;AAAA,IAAG;AAC7T,aAAS,mBAAmB,YAAY,SAAS;AAC/C,UAAI,OAAO,WAAW,cAAc,QAAQ,OAAO,QAAQ,MAAM,UAAU;AACzE,eAAO,eAAe,YAAY,OAAO,UAAU;AAAA,UACjD,OAAO,eAAe,QAAQ,KAAK,OAAO;AAAA,QAC5C,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;AChBA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,sBAAsB,uBAAuB,4BAAoC;AACrF,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,eAAe,GAAG,GAAG;AAAE,aAAO,gBAAgB,CAAC,KAAK,sBAAsB,GAAG,CAAC,KAAK,4BAA4B,GAAG,CAAC,KAAK,iBAAiB;AAAA,IAAG;AACrJ,aAAS,mBAAmB;AAAE,YAAM,IAAI,UAAU,2IAA2I;AAAA,IAAG;AAChM,aAAS,4BAA4B,GAAG,GAAG;AAAE,UAAI,GAAG;AAAE,YAAI,YAAY,OAAO,EAAG,QAAO,kBAAkB,GAAG,CAAC;AAAG,YAAI,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,eAAO,aAAa,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,UAAU,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC,IAAI,gBAAgB,KAAK,2CAA2C,KAAK,CAAC,IAAI,kBAAkB,GAAG,CAAC,IAAI;AAAA,MAAQ;AAAA,IAAE;AACzX,aAAS,kBAAkB,GAAG,GAAG;AAAE,OAAC,QAAQ,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE;AAAS,eAAS,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC;AAAG,aAAO;AAAA,IAAG;AACnJ,aAAS,sBAAsB,GAAG,GAAG;AAAE,UAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAAG,UAAI,QAAQ,GAAG;AAAE,YAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,MAAI,IAAI;AAAI,YAAI;AAAE,cAAI,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG;AAAE,gBAAI,OAAO,CAAC,MAAM,EAAG;AAAQ,gBAAI;AAAA,UAAI,MAAO,QAAO,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,WAAW,IAAI,IAAI,KAAG;AAAA,QAAE,SAASC,IAAG;AAAE,cAAI,MAAI,IAAIA;AAAA,QAAG,UAAE;AAAU,cAAI;AAAE,gBAAI,CAAC,KAAK,QAAQ,EAAE,WAAW,IAAI,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,GAAI;AAAA,UAAQ,UAAE;AAAU,gBAAI,EAAG,OAAM;AAAA,UAAG;AAAA,QAAE;AAAE,eAAO;AAAA,MAAG;AAAA,IAAE;AACnhB,aAAS,gBAAgB,GAAG;AAAE,UAAI,MAAM,QAAQ,CAAC,EAAG,QAAO;AAAA,IAAG;AAC9D,QAAI,aAAa,CAAC,CAAC,yBAAyB;AAAA,MAC1C,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,eAAe;AAAA,MAClB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,qBAAqB;AAAA,MACxB,QAAQ;AAAA,MACR,UAAU,CAAC,UAAU,QAAQ,QAAQ,MAAM;AAAA,IAC7C,CAAC,GAAG,CAAC,qBAAqB;AAAA,MACxB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,+BAA+B;AAAA,MAClC,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,aAAa;AAAA,MAChB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,gBAAgB;AAAA,MACnB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,gBAAgB;AAAA,MACnB,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,gBAAgB;AAAA,MACnB,MAAM;AAAA,MACN,QAAQ,CAAC,QAAQ,QAAQ,YAAY,QAAQ,QAAQ,MAAM,KAAK;AAAA,IAClE,CAAC,GAAG,CAAC,oBAAoB;AAAA,MACvB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,oBAAoB;AAAA,MACvB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,gBAAgB;AAAA,MACnB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,mBAAmB;AAAA,MACtB,QAAQ;AAAA,MACR,UAAU,CAAC,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,OAAO;AAAA,IAC/D,CAAC,GAAG,CAAC,qBAAqB;AAAA,MACxB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,MACR,kBAAkB;AAAA,IACpB,CAAC,GAAG,CAAC,eAAe;AAAA,MAClB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,gBAAgB;AAAA,MACnB,QAAQ;AAAA,MACR,kBAAkB;AAAA,IACpB,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,MACR,UAAU,CAAC,OAAO,MAAM,QAAQ,WAAW,QAAQ,QAAQ,QAAQ;AAAA,IACrE,CAAC,GAAG,CAAC,eAAe;AAAA,MAClB,QAAQ;AAAA,MACR,kBAAkB;AAAA,IACpB,CAAC,GAAG,CAAC,gBAAgB;AAAA,MACnB,QAAQ;AAAA,MACR,UAAU,CAAC,WAAW,OAAO,YAAY,IAAI;AAAA,IAC/C,CAAC,GAAG,CAAC,qBAAqB;AAAA,MACxB,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,cAAc;AAAA,MACjB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,mBAAmB;AAAA,MACtB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,cAAc;AAAA,MACjB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,aAAa;AAAA,MAChB,QAAQ;AAAA,MACR,UAAU,CAAC,aAAa,OAAO,QAAQ;AAAA,IACzC,CAAC,GAAG,CAAC,cAAc;AAAA,MACjB,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,kBAAkB;AAAA,MACrB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,wBAAwB;AAAA,MAC3B,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,oBAAoB;AAAA,MACvB,QAAQ;AAAA,MACR,UAAU,CAAC,YAAY,aAAa,YAAY;AAAA,IAClD,CAAC,GAAG,CAAC,aAAa;AAAA,MAChB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,oBAAoB;AAAA,MACvB,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,gBAAgB;AAAA,MACnB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,MACR,UAAU,CAAC,aAAa,OAAO,YAAY,MAAM;AAAA,IACnD,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,wBAAwB;AAAA,MAC3B,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,gBAAgB;AAAA,MACnB,MAAM;AAAA,IACR,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,MACR,kBAAkB;AAAA,IACpB,CAAC,GAAG,CAAC,gBAAgB;AAAA,MACnB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,aAAa;AAAA,MAChB,QAAQ;AAAA,MACR,UAAU,CAAC,aAAa,cAAc,QAAQ,OAAO;AAAA,IACvD,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,iBAAiB;AAAA,MACpB,QAAQ;AAAA,IACV,CAAC,GAAG,CAAC,kBAAkB;AAAA,MACrB,QAAQ;AAAA,IACV,CAAC,CAAC;AACF,QAAI,eAAe;AAAA,MACjB,SAAS,SAAS,UAAU;AAC1B,eAAO;AAAA,MACT;AAAA,MACA,SAAS,SAAS,QAAQ,IAAI;AAC5B,YAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,iBAAS,KAAK,GAAG,cAAc,YAAY,KAAK,YAAY,QAAQ,MAAM;AACxE,cAAI,iBAAiB,eAAe,YAAY,EAAE,GAAG,CAAC,GACpD,MAAM,eAAe,CAAC,GACtB,SAAS,eAAe,CAAC;AAC3B,aAAG,KAAK,SAAS,QAAQ,KAAK,UAAU;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,KAAK,SAAS,IAAI,KAAK;AACrB,YAAI,OAAO,WAAW,OAAO,SAAU,OAAO;AAC5C,iBAAO,MAAM,CAAC,MAAM,MAAM,OAAO;AAAA,QACnC,CAAC,EAAE,CAAC;AACJ,eAAO,QAAQ,KAAK,CAAC;AAAA,MACvB;AAAA,MACA,KAAK,SAAS,IAAI,KAAK;AACrB,eAAO,CAAC,CAAC,aAAa,IAAI,GAAG;AAAA,MAC/B;AAAA,MACA,MAAM,SAAS,OAAO;AACpB,eAAO,WAAW,IAAI,SAAU,MAAM;AACpC,cAAI,QAAQ,eAAe,MAAM,CAAC,GAChC,MAAM,MAAM,CAAC;AACf,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,MACA,QAAQ,SAAS,SAAS;AACxB,eAAO,WAAW,IAAI,SAAU,OAAO;AACrC,cAAI,QAAQ,eAAe,OAAO,CAAC,GACjCC,UAAS,MAAM,CAAC;AAClB,iBAAOA;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,WAAW,GAAG,oBAAoB,SAAS,cAAc,aAAa,QAAQ,CAAC;AAAA;AAAA;;;ACvKtG;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,sBAAsB,uBAAuB,4BAAoC;AACrF,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,eAAe,GAAG,GAAG;AAAE,aAAO,gBAAgB,CAAC,KAAK,sBAAsB,GAAG,CAAC,KAAK,4BAA4B,GAAG,CAAC,KAAK,iBAAiB;AAAA,IAAG;AACrJ,aAAS,mBAAmB;AAAE,YAAM,IAAI,UAAU,2IAA2I;AAAA,IAAG;AAChM,aAAS,4BAA4B,GAAG,GAAG;AAAE,UAAI,GAAG;AAAE,YAAI,YAAY,OAAO,EAAG,QAAO,kBAAkB,GAAG,CAAC;AAAG,YAAI,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,eAAO,aAAa,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,UAAU,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC,IAAI,gBAAgB,KAAK,2CAA2C,KAAK,CAAC,IAAI,kBAAkB,GAAG,CAAC,IAAI;AAAA,MAAQ;AAAA,IAAE;AACzX,aAAS,kBAAkB,GAAG,GAAG;AAAE,OAAC,QAAQ,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE;AAAS,eAAS,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC;AAAG,aAAO;AAAA,IAAG;AACnJ,aAAS,sBAAsB,GAAG,GAAG;AAAE,UAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAAG,UAAI,QAAQ,GAAG;AAAE,YAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,MAAI,IAAI;AAAI,YAAI;AAAE,cAAI,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG;AAAE,gBAAI,OAAO,CAAC,MAAM,EAAG;AAAQ,gBAAI;AAAA,UAAI,MAAO,QAAO,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,WAAW,IAAI,IAAI,KAAG;AAAA,QAAE,SAASC,IAAG;AAAE,cAAI,MAAI,IAAIA;AAAA,QAAG,UAAE;AAAU,cAAI;AAAE,gBAAI,CAAC,KAAK,QAAQ,EAAE,WAAW,IAAI,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,GAAI;AAAA,UAAQ,UAAE;AAAU,gBAAI,EAAG,OAAM;AAAA,UAAG;AAAA,QAAE;AAAE,eAAO;AAAA,MAAG;AAAA,IAAE;AACnhB,aAAS,gBAAgB,GAAG;AAAE,UAAI,MAAM,QAAQ,CAAC,EAAG,QAAO;AAAA,IAAG;AAC9D,QAAI,MAAM,CAAC,CAAC,KAAK;AAAA,MACf,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,KAAK;AAAA,MACR,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,cAAc;AAAA,MACjB,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,YAAY;AAAA,MACf,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,YAAY;AAAA,MACf,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,YAAY;AAAA,MACf,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,cAAc;AAAA,MACjB,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,YAAY;AAAA,MACf,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,KAAK;AAAA,MACR,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,YAAY;AAAA,MACf,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,YAAY;AAAA,MACf,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,YAAY;AAAA,MACf,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,KAAK;AAAA,MACR,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,YAAY;AAAA,MACf,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,KAAK;AAAA,MACR,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,KAAK;AAAA,MACR,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,UAAU;AAAA,MACb,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,WAAW;AAAA,MACd,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,YAAY;AAAA,MACf,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,QAAQ;AAAA,MACX,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,KAAK;AAAA,MACR,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AAAA,MACT,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,SAAS;AAAA,MACZ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,OAAO;AAAA,MACV,UAAU;AAAA,IACZ,CAAC,CAAC;AACF,QAAI,SAAS;AAAA,MACX,SAAS,SAAS,UAAU;AAC1B,eAAO;AAAA,MACT;AAAA,MACA,SAAS,SAAS,QAAQ,IAAI;AAC5B,YAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,iBAAS,KAAK,GAAG,OAAO,KAAK,KAAK,KAAK,QAAQ,MAAM;AACnD,cAAI,UAAU,eAAe,KAAK,EAAE,GAAG,CAAC,GACtC,MAAM,QAAQ,CAAC,GACf,SAAS,QAAQ,CAAC;AACpB,aAAG,KAAK,SAAS,QAAQ,KAAK,GAAG;AAAA,QACnC;AAAA,MACF;AAAA,MACA,KAAK,SAAS,IAAI,KAAK;AACrB,YAAI,OAAO,IAAI,OAAO,SAAU,OAAO;AACrC,iBAAO,MAAM,CAAC,MAAM,MAAM,OAAO;AAAA,QACnC,CAAC,EAAE,CAAC;AACJ,eAAO,QAAQ,KAAK,CAAC;AAAA,MACvB;AAAA,MACA,KAAK,SAAS,IAAI,KAAK;AACrB,eAAO,CAAC,CAAC,OAAO,IAAI,GAAG;AAAA,MACzB;AAAA,MACA,MAAM,SAAS,OAAO;AACpB,eAAO,IAAI,IAAI,SAAU,MAAM;AAC7B,cAAI,QAAQ,eAAe,MAAM,CAAC,GAChC,MAAM,MAAM,CAAC;AACf,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,MACA,QAAQ,SAAS,SAAS;AACxB,eAAO,IAAI,IAAI,SAAU,OAAO;AAC9B,cAAI,QAAQ,eAAe,OAAO,CAAC,GACjCC,UAAS,MAAM,CAAC;AAClB,iBAAOA;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,WAAW,GAAG,oBAAoB,SAAS,QAAQ,OAAO,QAAQ,CAAC;AAAA;AAAA;;;ACtT1F;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,QAAQ,CAAC;AAAA,IACrC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,QAAQ,CAAC;AAAA,IACrC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACxBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,YAAY;AAAA,MACd,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,QAAQ,CAAC;AAAA,IACrC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC5BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,YAAY;AAAA,MACd,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACzBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC;AAAA,MACX,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,eAAe;AAAA,QACf,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,oBAAoB;AAAA,QACpB,gBAAgB;AAAA,QAChB,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,qBAAqB;AAAA,QACrB,cAAc;AAAA,QACd,mBAAmB;AAAA,QACnB,aAAa;AAAA,QACb,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,wBAAwB;AAAA,MAC1B;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC;AAAA,IACf;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACjDjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC;AAAA,MACX,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACpCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,WAAW,GAAG,CAAC,YAAY,aAAa,WAAW,OAAO,CAAC;AAAA,IACjG;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACvBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC;AAAA,MACX,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,UAAU,CAAC;AAAA,IAC3B;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC;AAAA,MACX,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,UAAU,CAAC;AAAA,IAC3B;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,cAAc;AAAA,MAChB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,UAAU,CAAC;AAAA,IAC3B;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACvBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe,uBAAuB,qBAAiC;AAC3E,QAAI,iBAAiB,uBAAuB,uBAAmC;AAC/E,QAAI,aAAa,uBAAuB,mBAA+B;AACvE,QAAI,gBAAgB,uBAAuB,sBAAkC;AAC7E,QAAI,aAAa,uBAAuB,mBAA+B;AACvE,QAAI,gBAAgB,uBAAuB,sBAAkC;AAC7E,QAAI,eAAe,uBAAuB,qBAAiC;AAC3E,QAAI,mBAAmB,uBAAuB,yBAAqC;AACnF,QAAI,cAAc,uBAAuB,oBAAgC;AACzE,QAAI,iBAAiB,uBAAuB,uBAAmC;AAC/E,QAAI,cAAc,uBAAuB,oBAAgC;AACzE,QAAI,cAAc,uBAAuB,oBAAgC;AACzE,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,QAAI,oBAAoB,CAAC,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,aAAa,eAAe,OAAO,GAAG,CAAC,SAAS,WAAW,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,SAAS,WAAW,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,eAAe,iBAAiB,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,aAAa,eAAe,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,CAAC;AACjc,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACpBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,YAAY;AAAA,MACd,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,eAAe;AAAA,QACf,aAAa;AAAA,MACf;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC7BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,OAAO,GAAG,CAAC,YAAY,UAAU,QAAQ,CAAC;AAAA,IAC9F;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,UAAU,CAAC;AAAA,IACpD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC7BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,aAAa,CAAC,4BAA4B;AAAA,UAC1C,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,SAAS,CAAC;AAAA,IAChD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACxEjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,UAAU,QAAQ,OAAO;AAAA,MAC9C,qBAAqB,CAAC,UAAU,QAAQ,OAAO;AAAA,MAC/C,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,aAAa,CAAC,uCAAuC;AAAA,UACrD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,KAAK;AAAA,MAC1B,qBAAqB,CAAC,KAAK;AAAA,MAC3B,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,gBAAgB;AAAA,QAChB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,UAAU,OAAO,CAAC;AAAA,IAC9C;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC5CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,mBAAmB;AAAA,MACrB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,aAAa;AAAA,MACf;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,KAAK;AAAA,MAC1B,qBAAqB,CAAC,KAAK;AAAA,MAC3B,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,MAAM,GAAG,CAAC,YAAY,aAAa,WAAW,QAAQ,UAAU,GAAG,CAAC,YAAY,UAAU,UAAU,GAAG,CAAC,YAAY,aAAa,aAAa,CAAC;AAAA,IACnM;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC9CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,yBAAyB;AAAA,QACzB,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,aAAa,CAAC,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,aAAa,CAAC,+FAA+F;AAAA,UAC7G,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,UAAU,OAAO,CAAC;AAAA,IAC9C;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3HjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,oBAAoB;AAAA,MACtB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,aAAa,CAAC,8BAA8B,4BAA4B;AAAA,UACxE,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,aAAa,CAAC,0CAA0C,qDAAqD;AAAA,UAC7G,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,aAAa,CAAC,0CAA0C,qDAAqD;AAAA,UAC7G,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC/CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,aAAa,CAAC,4BAA4B;AAAA,UAC1C,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,QAAQ,CAAC;AAAA,IACrC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,MAAM,CAAC;AAAA,IAC3D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACvBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC9BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,SAAS,CAAC;AAAA,MACnC,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,MAAM,CAAC;AAAA,IAC3D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChDjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,aAAa,CAAC,8BAA8B,0CAA0C,qDAAqD;AAAA,UAC3I,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,aAAa,CAAC,8BAA8B,0CAA0C,qDAAqD;AAAA,UAC3I,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACtHjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,KAAK,GAAG,CAAC,OAAO,UAAU,CAAC;AAAA,MACpD,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,WAAW,GAAG,CAAC,YAAY,aAAa,WAAW,OAAO,CAAC;AAAA,IACjG;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACxBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,aAAa,CAAC,wCAAwC,0CAA0C;AAAA,UAChG,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,KAAK;AAAA,MAC1B,qBAAqB,CAAC,KAAK;AAAA,MAC3B,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,MAAM,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,IACnF;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACpCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,YAAY;AAAA,MACd,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC5CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,cAAc;AAAA,MAChB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,cAAc;AAAA,MAChB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,aAAa,aAAa,CAAC;AAAA,IACvD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACvDjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,UAAU;AAAA,MACZ,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC5CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,SAAS,CAAC;AAAA,IAChD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,UAAU,CAAC;AAAA,MACpC,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACpCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,MACtB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,IAAI;AAAA,YAClB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,aAAa,CAAC,4CAA4C;AAAA,UAC1D,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,UACR,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,UAAU,OAAO,GAAG,CAAC,QAAQ,CAAC;AAAA,MACvD,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,aAAa,QAAQ,GAAG,CAAC,YAAY,aAAa,WAAW,SAAS,QAAQ,CAAC;AAAA,IACrH;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC9DjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,aAAa,CAAC,2BAA2B,2BAA2B,2BAA2B;AAAA,UAC/F,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,aAAa,MAAM;AAAA,MACxC,qBAAqB,CAAC,aAAa,MAAM;AAAA,MACzC,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACpCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,UAAU;AAAA,MACZ,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,aAAa;AAAA,MACf;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACvBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,+BAA+B;AAAA,QAC/B,oBAAoB;AAAA,MACtB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC9BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,YAAY,OAAO,GAAG,CAAC,iBAAiB,OAAO,GAAG,CAAC,oBAAoB,OAAO,GAAG,CAAC,UAAU,GAAG,CAAC,kBAAkB,GAAG,CAAC,eAAe,CAAC;AAAA,MAC/J,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,aAAa,QAAQ,GAAG,CAAC,YAAY,aAAa,WAAW,SAAS,QAAQ,CAAC;AAAA,IACrH;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,YAAY,OAAO,GAAG,CAAC,iBAAiB,OAAO,GAAG,CAAC,oBAAoB,OAAO,GAAG,CAAC,UAAU,GAAG,CAAC,kBAAkB,GAAG,CAAC,eAAe,CAAC;AAAA,MAC/J,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,aAAa,UAAU,MAAM,GAAG,CAAC,YAAY,aAAa,WAAW,SAAS,UAAU,MAAM,CAAC;AAAA,IACrI;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC5BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,SAAS,QAAQ,SAAS;AAAA,MAC/C,qBAAqB,CAAC,SAAS,QAAQ,SAAS;AAAA,MAChD,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,SAAS,CAAC;AAAA,IAChD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,uBAAuB;AAAA,MACzB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,SAAS,QAAQ,SAAS;AAAA,MAC/C,qBAAqB,CAAC,SAAS,QAAQ,SAAS;AAAA,MAChD,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,UAAU,SAAS,UAAU,GAAG,CAAC,YAAY,UAAU,WAAW,UAAU,CAAC;AAAA,IACzG;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC5BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,oBAAoB;AAAA,MACtB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,SAAS,QAAQ,SAAS;AAAA,MAC/C,qBAAqB,CAAC,SAAS,QAAQ,SAAS;AAAA,MAChD,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,UAAU,SAAS,YAAY,kBAAkB,GAAG,CAAC,YAAY,UAAU,WAAW,YAAY,kBAAkB,GAAG,CAAC,YAAY,UAAU,SAAS,OAAO,CAAC;AAAA,IAC3L;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC5BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,YAAY;AAAA,MACd,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,aAAa,OAAO,CAAC;AAAA,IACjD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC;AAAA,MACX,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC;AAAA,IACf;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,UAAU,OAAO,CAAC;AAAA,IAC9C;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,mBAAmB;AAAA,MACrB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC9BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,kBAAkB;AAAA,MACpB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,OAAO,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,IACzE;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACjCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,YAAY;AAAA,MACd,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,UAAU,OAAO,CAAC;AAAA,IAC9C;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACpCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,OAAO,CAAC;AAAA,MACjC,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,aAAa,QAAQ,GAAG,CAAC,YAAY,aAAa,WAAW,SAAS,QAAQ,CAAC;AAAA,IACrH;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC/BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,KAAK;AAAA,YACnB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,UAAU;AAAA,MACZ,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,QAAQ,YAAY,SAAS,UAAU;AAAA,MAC5D,qBAAqB,CAAC,QAAQ,YAAY,SAAS,UAAU;AAAA,MAC7D,uBAAuB,CAAC,CAAC,MAAM,GAAG,CAAC,cAAc,GAAG,CAAC,UAAU,GAAG,CAAC,WAAW,CAAC;AAAA,MAC/E,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,OAAO,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,IACpF;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AClCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,QAAQ,SAAS,UAAU;AAAA,MAChD,qBAAqB,CAAC,QAAQ,SAAS,UAAU;AAAA,MACjD,uBAAuB,CAAC,CAAC,KAAK,CAAC;AAAA,MAC/B,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACpCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,aAAa;AAAA,MACf;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,OAAO,UAAU;AAAA,MACtC,qBAAqB,CAAC,OAAO,UAAU;AAAA,MACvC,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,MAAM,GAAG,CAAC,YAAY,aAAa,WAAW,QAAQ,UAAU,GAAG,CAAC,YAAY,UAAU,UAAU,GAAG,CAAC,YAAY,aAAa,aAAa,CAAC;AAAA,IACnM;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACzCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,aAAa,OAAO,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,IACzE;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC9BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,aAAa,CAAC,+BAA+B;AAAA,UAC7C,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,SAAS,SAAS,CAAC;AAAA,IACzD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AClCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,QACpB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MACpB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,WAAW,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACjCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,UAAU,OAAO,GAAG,CAAC,YAAY,aAAa,OAAO,CAAC;AAAA,IAClF;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACzCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,WAAW,GAAG,CAAC,YAAY,UAAU,OAAO,GAAG,CAAC,YAAY,aAAa,OAAO,CAAC;AAAA,IACvH;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,eAAe;AAAA,QACf,aAAa;AAAA,MACf;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC7BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC,cAAc,iBAAiB;AAAA,MACjD,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,gBAAgB;AAAA,MAClB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,UAAU,SAAS,UAAU,CAAC;AAAA,IAC1D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC5BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,UAAU;AAAA,MACZ,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC,SAAS;AAAA,MAC9B,qBAAqB,CAAC,SAAS;AAAA,MAC/B,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,aAAa,GAAG,CAAC,YAAY,QAAQ,CAAC;AAAA,IAC/E;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC5BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,YAAY;AAAA,MACd,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,KAAK,GAAG,CAAC,OAAO,UAAU,CAAC;AAAA,MACpD,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC7BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,oBAAoB;AAAA,MACtB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,KAAK,CAAC;AAAA,MAC/B,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,WAAW,CAAC;AAAA,IAClD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC9BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC/BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,yBAAyB;AAAA,QACzB,qBAAqB;AAAA,QACrB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,kBAAkB;AAAA,QAClB,oBAAoB;AAAA,QACpB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,aAAa,CAAC,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,CAAC;AAAA,UACD,aAAa,CAAC,+BAA+B;AAAA,UAC7C,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,aAAa,CAAC,+BAA+B;AAAA,UAC7C,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,aAAa,CAAC,+BAA+B;AAAA,UAC7C,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,aAAa,CAAC,+BAA+B;AAAA,UAC7C,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,YAAY,CAAC;AAAA,YACX,aAAa,CAAC,WAAW;AAAA,YACzB,MAAM;AAAA,UACR,GAAG;AAAA,YACD,MAAM;AAAA,YACN,OAAO;AAAA,UACT,CAAC;AAAA,UACD,aAAa,CAAC,+BAA+B;AAAA,UAC7C,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,GAAG;AAAA,QACD,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,OAAO,CAAC;AAAA,IAC9C;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1GjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,YAAY;AAAA,MACd,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,QAAQ,CAAC;AAAA,IAC7D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,oBAAoB;AAAA,MACtB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,OAAO,CAAC;AAAA,IAC5D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC5BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,WAAW;AAAA,MACb,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,QACjB,oBAAoB;AAAA,MACtB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,YAAY,OAAO,GAAG,CAAC,UAAU,CAAC;AAAA,MAC3D,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,aAAa,QAAQ,GAAG,CAAC,YAAY,aAAa,WAAW,SAAS,QAAQ,CAAC;AAAA,IACrH;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,KAAK,GAAG,CAAC,OAAO,UAAU,CAAC;AAAA,MACpD,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,aAAa,MAAM,GAAG,CAAC,YAAY,aAAa,WAAW,SAAS,MAAM,GAAG,CAAC,YAAY,UAAU,aAAa,UAAU,MAAM,GAAG,CAAC,YAAY,aAAa,WAAW,SAAS,UAAU,MAAM,CAAC;AAAA,IACzO;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACrBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC,SAAS,MAAM;AAAA,MACpC,qBAAqB,CAAC,SAAS,MAAM;AAAA,MACrC,uBAAuB,CAAC;AAAA,MACxB,eAAe;AAAA,QACb,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,GAAG,CAAC,YAAY,UAAU,SAAS,QAAQ,CAAC;AAAA,IAC1G;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa,uBAAuB,mBAA8B;AACtE,QAAI,mBAAmB,uBAAuB,yBAAoC;AAClF,QAAI,mBAAmB,uBAAuB,yBAAoC;AAClF,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,kBAAkB,uBAAuB,wBAAmC;AAChF,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,oBAAoB,uBAAuB,0BAAqC;AACpF,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,qBAAqB,uBAAuB,2BAAsC;AACtF,QAAI,mBAAmB,uBAAuB,yBAAoC;AAClF,QAAI,kBAAkB,uBAAuB,wBAAmC;AAChF,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,iBAAiB,uBAAuB,uBAAkC;AAC9E,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,aAAa,uBAAuB,mBAA8B;AACtE,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,WAAW,uBAAuB,iBAA4B;AAClE,QAAI,iBAAiB,uBAAuB,uBAAkC;AAC9E,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,WAAW,uBAAuB,iBAA4B;AAClE,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,wBAAwB,uBAAuB,8BAAyC;AAC5F,QAAI,qBAAqB,uBAAuB,2BAAsC;AACtF,QAAI,aAAa,uBAAuB,mBAA8B;AACtE,QAAI,kBAAkB,uBAAuB,wBAAmC;AAChF,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,iBAAiB,uBAAuB,uBAAkC;AAC9E,QAAI,oBAAoB,uBAAuB,0BAAqC;AACpF,QAAI,mBAAmB,uBAAuB,yBAAoC;AAClF,QAAI,aAAa,uBAAuB,mBAA8B;AACtE,QAAI,kBAAkB,uBAAuB,wBAAmC;AAChF,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,WAAW,uBAAuB,iBAA4B;AAClE,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,iBAAiB,uBAAuB,uBAAkC;AAC9E,QAAI,iBAAiB,uBAAuB,uBAAkC;AAC9E,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,iBAAiB,uBAAuB,uBAAkC;AAC9E,QAAI,iBAAiB,uBAAuB,uBAAkC;AAC9E,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,kBAAkB,uBAAuB,wBAAmC;AAChF,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,iBAAiB,uBAAuB,uBAAkC;AAC9E,QAAI,mBAAmB,uBAAuB,yBAAoC;AAClF,QAAI,cAAc,uBAAuB,oBAA+B;AACxE,QAAI,WAAW,uBAAuB,iBAA4B;AAClE,QAAI,aAAa,uBAAuB,mBAA8B;AACtE,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,aAAa,uBAAuB,mBAA8B;AACtE,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,eAAe,uBAAuB,qBAAgC;AAC1E,QAAI,YAAY,uBAAuB,kBAA6B;AACpE,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,QAAI,gBAAgB,uBAAuB,sBAAiC;AAC5E,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,QAAI,mBAAmB,CAAC,CAAC,SAAS,WAAW,OAAO,GAAG,CAAC,eAAe,iBAAiB,OAAO,GAAG,CAAC,eAAe,iBAAiB,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,cAAc,gBAAgB,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,gBAAgB,kBAAkB,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,iBAAiB,mBAAmB,OAAO,GAAG,CAAC,eAAe,iBAAiB,OAAO,GAAG,CAAC,cAAc,gBAAgB,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,aAAa,eAAe,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,SAAS,WAAW,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,OAAO,SAAS,OAAO,GAAG,CAAC,aAAa,eAAe,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,OAAO,SAAS,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,oBAAoB,sBAAsB,OAAO,GAAG,CAAC,iBAAiB,mBAAmB,OAAO,GAAG,CAAC,SAAS,WAAW,OAAO,GAAG,CAAC,cAAc,gBAAgB,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,aAAa,eAAe,OAAO,GAAG,CAAC,gBAAgB,kBAAkB,OAAO,GAAG,CAAC,eAAe,iBAAiB,OAAO,GAAG,CAAC,SAAS,WAAW,OAAO,GAAG,CAAC,cAAc,gBAAgB,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,OAAO,SAAS,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,aAAa,eAAe,OAAO,GAAG,CAAC,aAAa,eAAe,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,aAAa,eAAe,OAAO,GAAG,CAAC,aAAa,eAAe,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,cAAc,gBAAgB,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,aAAa,eAAe,OAAO,GAAG,CAAC,eAAe,iBAAiB,OAAO,GAAG,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,OAAO,SAAS,OAAO,GAAG,CAAC,SAAS,WAAW,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,SAAS,WAAW,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,WAAW,aAAa,OAAO,GAAG,CAAC,QAAQ,UAAU,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,GAAG,CAAC,YAAY,cAAc,OAAO,CAAC;AAC73F,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3FjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,yBAAyB;AAAA,MAC3B,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,mBAAmB;AAAA,MACrB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,WAAW,MAAM,CAAC;AAAA,IACxD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC7BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,qBAAqB;AAAA,MACvB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,kBAAkB;AAAA,MACvC,qBAAqB,CAAC,kBAAkB;AAAA,MACxC,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,sBAAsB;AAAA,MACxB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC3C,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,mBAAmB;AAAA,MACrB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,WAAW,MAAM,CAAC;AAAA,IACxD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC7BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,oBAAoB;AAAA,MACtB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,KAAK,CAAC;AAAA,IAC1D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,oBAAoB;AAAA,MACtB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC,cAAc;AAAA,MACnC,qBAAqB,CAAC,cAAc;AAAA,MACpC,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,aAAa,CAAC;AAAA,MACvC,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC,CAAC,YAAY,GAAG,CAAC,MAAM,CAAC;AAAA,MAChD,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,WAAW,MAAM,CAAC;AAAA,IACxD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC7BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,eAAe;AAAA,MACjB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,YAAY,YAAY,CAAC;AAAA,IAC7E;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,sBAAsB;AAAA,MACxB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,UAAU,WAAW,MAAM,CAAC;AAAA,IACxD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC7BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB;AAAA,MAClB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,MAAM,CAAC;AAAA,IAC3D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,mBAAmB;AAAA,MACrB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,CAAC;AAAA,IACrD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC/BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,oBAAoB;AAAA,MACtB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,+BAA+B;AAAA,QAC/B,oBAAoB;AAAA,QACpB,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC7BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,oBAAoB;AAAA,MACtB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,YAAY;AAAA,MACvB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,+BAA+B;AAAA,QAC/B,oBAAoB;AAAA,QACpB,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC7BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,YAAY,YAAY,CAAC;AAAA,IAC7E;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,cAAc;AAAA,MAChB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,iBAAiB;AAAA,MACnB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,UAAU,CAAC;AAAA,IAC/D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,mBAAmB;AAAA,MACrB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO,CAAC;AAAA,MACR,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,MAAM,CAAC;AAAA,IACvB;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,SAAS,CAAC;AAAA,IACnD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,kBAAkB;AAAA,MACpB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,aAAa,CAAC;AAAA,IACvD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,MAAM,CAAC;AAAA,IAC3D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,aAAa;AAAA,MACf,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,YAAY,YAAY,CAAC;AAAA,IAC7E;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AChCjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,0BAA0B,uBAAuB,gCAAwC;AAC7F,QAAI,oBAAoB,uBAAuB,0BAAkC;AACjF,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,sBAAsB,uBAAuB,4BAAoC;AACrF,QAAI,uBAAuB,uBAAuB,6BAAqC;AACvF,QAAI,oBAAoB,uBAAuB,0BAAkC;AACjF,QAAI,kBAAkB,uBAAuB,wBAAgC;AAC7E,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,qBAAqB,uBAAuB,2BAAmC;AACnF,QAAI,gBAAgB,uBAAuB,sBAA8B;AACzE,QAAI,iBAAiB,uBAAuB,uBAA+B;AAC3E,QAAI,kBAAkB,uBAAuB,wBAAgC;AAC7E,QAAI,qBAAqB,uBAAuB,2BAAmC;AACnF,QAAI,kBAAkB,uBAAuB,wBAAgC;AAC7E,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,iBAAiB,uBAAuB,uBAA+B;AAC3E,QAAI,kBAAkB,uBAAuB,wBAAgC;AAC7E,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,gBAAgB,uBAAuB,sBAA8B;AACzE,QAAI,uBAAuB,uBAAuB,6BAAqC;AACvF,QAAI,kBAAkB,uBAAuB,wBAAgC;AAC7E,QAAI,iBAAiB,uBAAuB,uBAA+B;AAC3E,QAAI,oBAAoB,uBAAuB,0BAAkC;AACjF,QAAI,qBAAqB,uBAAuB,2BAAmC;AACnF,QAAI,qBAAqB,uBAAuB,2BAAmC;AACnF,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,eAAe,uBAAuB,qBAA6B;AACvE,QAAI,kBAAkB,uBAAuB,wBAAgC;AAC7E,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,oBAAoB,uBAAuB,0BAAkC;AACjF,QAAI,cAAc,uBAAuB,oBAA4B;AACrE,QAAI,mBAAmB,uBAAuB,yBAAiC;AAC/E,QAAI,cAAc,uBAAuB,oBAA4B;AACrE,QAAI,cAAc,uBAAuB,oBAA4B;AACrE,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,QAAI,gBAAgB,CAAC,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,uBAAuB,wBAAwB,OAAO,GAAG,CAAC,iBAAiB,kBAAkB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,mBAAmB,oBAAoB,OAAO,GAAG,CAAC,oBAAoB,qBAAqB,OAAO,GAAG,CAAC,iBAAiB,kBAAkB,OAAO,GAAG,CAAC,eAAe,gBAAgB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,kBAAkB,mBAAmB,OAAO,GAAG,CAAC,aAAa,cAAc,OAAO,GAAG,CAAC,cAAc,eAAe,OAAO,GAAG,CAAC,eAAe,gBAAgB,OAAO,GAAG,CAAC,kBAAkB,mBAAmB,OAAO,GAAG,CAAC,eAAe,gBAAgB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,cAAc,eAAe,OAAO,GAAG,CAAC,eAAe,gBAAgB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,aAAa,cAAc,OAAO,GAAG,CAAC,oBAAoB,qBAAqB,OAAO,GAAG,CAAC,eAAe,gBAAgB,OAAO,GAAG,CAAC,cAAc,eAAe,OAAO,GAAG,CAAC,iBAAiB,kBAAkB,OAAO,GAAG,CAAC,kBAAkB,mBAAmB,OAAO,GAAG,CAAC,kBAAkB,mBAAmB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,YAAY,aAAa,OAAO,GAAG,CAAC,eAAe,gBAAgB,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,iBAAiB,kBAAkB,OAAO,GAAG,CAAC,WAAW,YAAY,OAAO,GAAG,CAAC,gBAAgB,iBAAiB,OAAO,GAAG,CAAC,WAAW,YAAY,OAAO,GAAG,CAAC,WAAW,YAAY,OAAO,CAAC;AACjxD,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACjDjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,uBAAuB;AAAA,MACzB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,UAAU,CAAC;AAAA,IACpD;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC1CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,qBAAqB;AAAA,MACvB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,UAAU,UAAU;AAAA,MAC/B,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,QAChB,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,GAAG;AAAA,QACD,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,MAAM;AAAA,QACR;AAAA,MACF,CAAC;AAAA,MACD,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,OAAO,CAAC;AAAA,IAC5D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC9CjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,qBAAqB;AAAA,MACvB,UAAU;AAAA,MACV,wBAAwB;AAAA,MACxB,cAAc,CAAC;AAAA,MACf,wBAAwB;AAAA,MACxB,UAAU,CAAC,QAAQ;AAAA,MACnB,iBAAiB,CAAC;AAAA,MAClB,OAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,qBAAqB,CAAC;AAAA,MACtB,uBAAuB,CAAC;AAAA,MACxB,eAAe,CAAC;AAAA,MAChB,YAAY,CAAC,CAAC,YAAY,aAAa,WAAW,KAAK,CAAC;AAAA,IAC1D;AACA,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;AC3BjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,wBAAwB,uBAAuB,8BAA0C;AAC7F,QAAI,sBAAsB,uBAAuB,4BAAwC;AACzF,QAAI,sBAAsB,uBAAuB,4BAAwC;AACzF,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,QAAI,oBAAoB,CAAC,CAAC,qBAAqB,sBAAsB,OAAO,GAAG,CAAC,mBAAmB,oBAAoB,OAAO,GAAG,CAAC,mBAAmB,oBAAoB,OAAO,CAAC;AACjL,QAAI,WAAW,QAAQ,UAAU;AAAA;AAAA;;;ACXjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,qBAAqB,uBAAuB,2BAAwC;AACxF,QAAI,oBAAoB,uBAAuB,0BAAuC;AACtF,QAAI,iBAAiB,uBAAuB,uBAAoC;AAChF,QAAI,qBAAqB,uBAAuB,2BAAwC;AACxF,QAAI,sBAAsB,uBAAuB,4BAAoC;AACrF,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,2BAA2B,GAAG,GAAG;AAAE,UAAI,IAAI,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAAG,UAAI,CAAC,GAAG;AAAE,YAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,4BAA4B,CAAC,MAAM,KAAK,KAAK,YAAY,OAAO,EAAE,QAAQ;AAAE,gBAAM,IAAI;AAAI,cAAI,KAAK,GAAG,IAAI,SAASC,KAAI;AAAA,UAAC;AAAG,iBAAO,EAAE,GAAG,GAAG,GAAG,SAAS,IAAI;AAAE,mBAAO,MAAM,EAAE,SAAS,EAAE,MAAM,KAAG,IAAI,EAAE,MAAM,OAAI,OAAO,EAAE,IAAI,EAAE;AAAA,UAAG,GAAG,GAAG,SAASC,GAAEC,IAAG;AAAE,kBAAMA;AAAA,UAAG,GAAG,GAAG,EAAE;AAAA,QAAG;AAAE,cAAM,IAAI,UAAU,uIAAuI;AAAA,MAAG;AAAE,UAAI,GAAG,IAAI,MAAI,IAAI;AAAI,aAAO,EAAE,GAAG,SAAS,IAAI;AAAE,YAAI,EAAE,KAAK,CAAC;AAAA,MAAG,GAAG,GAAG,SAAS,IAAI;AAAE,YAAIA,KAAI,EAAE,KAAK;AAAG,eAAO,IAAIA,GAAE,MAAMA;AAAA,MAAG,GAAG,GAAG,SAASD,GAAEC,IAAG;AAAE,YAAI,MAAI,IAAIA;AAAA,MAAG,GAAG,GAAG,SAAS,IAAI;AAAE,YAAI;AAAE,eAAK,QAAQ,EAAE,UAAU,EAAE,OAAO;AAAA,QAAG,UAAE;AAAU,cAAI,EAAG,OAAM;AAAA,QAAG;AAAA,MAAE,EAAE;AAAA,IAAG;AACr1B,aAAS,eAAe,GAAG,GAAG;AAAE,aAAO,gBAAgB,CAAC,KAAK,sBAAsB,GAAG,CAAC,KAAK,4BAA4B,GAAG,CAAC,KAAK,iBAAiB;AAAA,IAAG;AACrJ,aAAS,mBAAmB;AAAE,YAAM,IAAI,UAAU,2IAA2I;AAAA,IAAG;AAChM,aAAS,4BAA4B,GAAG,GAAG;AAAE,UAAI,GAAG;AAAE,YAAI,YAAY,OAAO,EAAG,QAAO,kBAAkB,GAAG,CAAC;AAAG,YAAI,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,eAAO,aAAa,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,UAAU,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC,IAAI,gBAAgB,KAAK,2CAA2C,KAAK,CAAC,IAAI,kBAAkB,GAAG,CAAC,IAAI;AAAA,MAAQ;AAAA,IAAE;AACzX,aAAS,kBAAkB,GAAG,GAAG;AAAE,OAAC,QAAQ,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE;AAAS,eAAS,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC;AAAG,aAAO;AAAA,IAAG;AACnJ,aAAS,sBAAsB,GAAG,GAAG;AAAE,UAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAAG,UAAI,QAAQ,GAAG;AAAE,YAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,MAAI,IAAI;AAAI,YAAI;AAAE,cAAI,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG;AAAE,gBAAI,OAAO,CAAC,MAAM,EAAG;AAAQ,gBAAI;AAAA,UAAI,MAAO,QAAO,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,WAAW,IAAI,IAAI,KAAG;AAAA,QAAE,SAASA,IAAG;AAAE,cAAI,MAAI,IAAIA;AAAA,QAAG,UAAE;AAAU,cAAI;AAAE,gBAAI,CAAC,KAAK,QAAQ,EAAE,WAAW,IAAI,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,GAAI;AAAA,UAAQ,UAAE;AAAU,gBAAI,EAAG,OAAM;AAAA,UAAG;AAAA,QAAE;AAAE,eAAO;AAAA,MAAG;AAAA,IAAE;AACnhB,aAAS,gBAAgB,GAAG;AAAE,UAAI,MAAM,QAAQ,CAAC,EAAG,QAAO;AAAA,IAAG;AAC9D,QAAI,QAAQ,CAAC,EAAE,OAAO,mBAAmB,SAAS,kBAAkB,SAAS,eAAe,SAAS,mBAAmB,OAAO;AAC/H,UAAM,QAAQ,SAAU,MAAM;AAC5B,UAAI,QAAQ,eAAe,MAAM,CAAC,GAChC,iBAAiB,MAAM,CAAC;AAE1B,UAAI,YAAY,2BAA2B,eAAe,UAAU,GAClE;AACF,UAAI;AACF,aAAK,UAAU,EAAE,GAAG,EAAE,QAAQ,UAAU,EAAE,GAAG,QAAO;AAClD,cAAI,iBAAiB,MAAM;AAC3B,cAAI,aAAa,2BAA2B,cAAc,GACxD;AACF,cAAI;AACF,gBAAI,QAAQ,SAASC,SAAQ;AAC3B,kBAAI,iBAAiB,OAAO;AAC5B,kBAAI,sBAAsB,MAAM,OAAO,SAAU,OAAO;AACtD,oBAAI,QAAQ,eAAe,OAAO,CAAC,GACjC,OAAO,MAAM,CAAC;AAChB,uBAAO,SAAS;AAAA,cAClB,CAAC,EAAE,CAAC;AACJ,kBAAI,qBAAqB;AACvB,oBAAI,uBAAuB,oBAAoB,CAAC;AAChD,yBAAS,KAAK,GAAG,eAAe,OAAO,KAAK,qBAAqB,KAAK,GAAG,KAAK,aAAa,QAAQ,MAAM;AACvG,sBAAI,OAAO,aAAa,EAAE;AAC1B;AAAA;AAAA,oBAEA,CAAC,OAAO,UAAU,eAAe,KAAK,eAAe,OAAO,IAAI;AAAA,oBAAG;AAEjE,mCAAe,MAAM,IAAI,IAAI,qBAAqB,MAAM,IAAI;AAAA,kBAC9D;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,iBAAK,WAAW,EAAE,GAAG,EAAE,SAAS,WAAW,EAAE,GAAG,QAAO;AACrD,oBAAM;AAAA,YACR;AAAA,UACF,SAAS,KAAK;AACZ,uBAAW,EAAE,GAAG;AAAA,UAClB,UAAE;AACA,uBAAW,EAAE;AAAA,UACf;AAAA,QACF;AAAA,MACF,SAAS,KAAK;AACZ,kBAAU,EAAE,GAAG;AAAA,MACjB,UAAE;AACA,kBAAU,EAAE;AAAA,MACd;AAAA,IACF,CAAC;AACD,QAAI,WAAW;AAAA,MACb,SAAS,SAAS,UAAU;AAC1B,eAAO;AAAA,MACT;AAAA,MACA,SAAS,SAAS,QAAQ,IAAI;AAC5B,YAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,YAAI,aAAa,2BAA2B,KAAK,GAC/C;AACF,YAAI;AACF,eAAK,WAAW,EAAE,GAAG,EAAE,SAAS,WAAW,EAAE,GAAG,QAAO;AACrD,gBAAI,eAAe,eAAe,OAAO,OAAO,CAAC,GAC/C,MAAM,aAAa,CAAC,GACpB,SAAS,aAAa,CAAC;AACzB,eAAG,KAAK,SAAS,QAAQ,KAAK,KAAK;AAAA,UACrC;AAAA,QACF,SAAS,KAAK;AACZ,qBAAW,EAAE,GAAG;AAAA,QAClB,UAAE;AACA,qBAAW,EAAE;AAAA,QACf;AAAA,MACF;AAAA,MACA,KAAK,SAAS,IAAI,KAAK;AACrB,YAAI,OAAO,MAAM,OAAO,SAAU,OAAO;AACvC,iBAAO,MAAM,CAAC,MAAM,MAAM,OAAO;AAAA,QACnC,CAAC,EAAE,CAAC;AACJ,eAAO,QAAQ,KAAK,CAAC;AAAA,MACvB;AAAA,MACA,KAAK,SAAS,IAAI,KAAK;AACrB,eAAO,CAAC,CAAC,SAAS,IAAI,GAAG;AAAA,MAC3B;AAAA,MACA,MAAM,SAAS,OAAO;AACpB,eAAO,MAAM,IAAI,SAAU,OAAO;AAChC,cAAI,QAAQ,eAAe,OAAO,CAAC,GACjC,MAAM,MAAM,CAAC;AACf,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,MACA,QAAQ,SAAS,SAAS;AACxB,eAAO,MAAM,IAAI,SAAU,OAAO;AAChC,cAAI,QAAQ,eAAe,OAAO,CAAC,GACjCC,UAAS,MAAM,CAAC;AAClB,iBAAOA;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,WAAW,GAAG,oBAAoB,SAAS,UAAU,SAAS,QAAQ,CAAC;AAAA;AAAA;;;AChH9F;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,sBAAsB,uBAAuB,4BAAoC;AACrF,QAAI,YAAY,uBAAuB,kBAAqB;AAC5D,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,eAAe,GAAG,GAAG;AAAE,aAAO,gBAAgB,CAAC,KAAK,sBAAsB,GAAG,CAAC,KAAK,4BAA4B,GAAG,CAAC,KAAK,iBAAiB;AAAA,IAAG;AACrJ,aAAS,mBAAmB;AAAE,YAAM,IAAI,UAAU,2IAA2I;AAAA,IAAG;AAChM,aAAS,4BAA4B,GAAG,GAAG;AAAE,UAAI,GAAG;AAAE,YAAI,YAAY,OAAO,EAAG,QAAO,kBAAkB,GAAG,CAAC;AAAG,YAAI,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,eAAO,aAAa,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,UAAU,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC,IAAI,gBAAgB,KAAK,2CAA2C,KAAK,CAAC,IAAI,kBAAkB,GAAG,CAAC,IAAI;AAAA,MAAQ;AAAA,IAAE;AACzX,aAAS,kBAAkB,GAAG,GAAG;AAAE,OAAC,QAAQ,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE;AAAS,eAAS,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC;AAAG,aAAO;AAAA,IAAG;AACnJ,aAAS,sBAAsB,GAAG,GAAG;AAAE,UAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAAG,UAAI,QAAQ,GAAG;AAAE,YAAI,GAAG,GAAGC,IAAG,GAAG,IAAI,CAAC,GAAG,IAAI,MAAI,IAAI;AAAI,YAAI;AAAE,cAAIA,MAAK,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG;AAAE,gBAAI,OAAO,CAAC,MAAM,EAAG;AAAQ,gBAAI;AAAA,UAAI,MAAO,QAAO,EAAE,KAAK,IAAIA,GAAE,KAAK,CAAC,GAAG,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,WAAW,IAAI,IAAI,KAAG;AAAA,QAAE,SAASC,IAAG;AAAE,cAAI,MAAI,IAAIA;AAAA,QAAG,UAAE;AAAU,cAAI;AAAE,gBAAI,CAAC,KAAK,QAAQ,EAAE,WAAW,IAAI,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,GAAI;AAAA,UAAQ,UAAE;AAAU,gBAAI,EAAG,OAAM;AAAA,UAAG;AAAA,QAAE;AAAE,eAAO;AAAA,MAAG;AAAA,IAAE;AACnhB,aAAS,gBAAgB,GAAG;AAAE,UAAI,MAAM,QAAQ,CAAC,EAAG,QAAO;AAAA,IAAG;AAC9D,QAAI,eAAe,CAAC;AACpB,QAAI,OAAO,UAAU,QAAQ,KAAK;AAClC,SAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,YAAM,KAAK,CAAC;AACZ,aAAO,UAAU,QAAQ,IAAI,GAAG;AACpC,UAAI,MAAM;AACJ,mBAAW,CAAC,EAAE,OAAO,KAAK,cAAc,KAAK,eAAe;AAC5D,gBAAQ,SAASC,SAAQ;AAC3B,cAAI,WAAW,SAAS,CAAC;AACzB,cAAI,SAAS,WAAW,QAAQ;AAC9B,gBAAI,UAAU,SAAS;AACvB,gBAAI,SAAS;AACX,kBAAI,sBAAsB,aAAa,OAAO,SAAUC,WAAU;AAChE,uBAAO,8BAA8BA,UAAS,CAAC,GAAG,OAAO;AAAA,cAC3D,CAAC,EAAE,CAAC;AACJ,kBAAI;AACJ,kBAAI,qBAAqB;AACvB,wBAAQ,oBAAoB,CAAC;AAAA,cAC/B,OAAO;AACL,wBAAQ,CAAC;AAAA,cACX;AACA,kBAAI,WAAW;AACf,uBAAS,KAAK,GAAG,KAAK,MAAM,QAAQ,MAAM;AACxC,oBAAI,MAAM,EAAE,MAAM,KAAK;AACrB,6BAAW;AACX;AAAA,gBACF;AAAA,cACF;AACA,kBAAI,UAAU;AACZ,sBAAM,KAAK,GAAG;AAAA,cAChB;AACA,kBAAI,CAAC,qBAAqB;AACxB,6BAAa,KAAK,CAAC,SAAS,KAAK,CAAC;AAAA,cACpC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAtCM;AACA;AAEE;AACA;AA8BK;AAnCJ;AAwCT,QAAI,iBAAiB;AAAA,MACnB,SAAS,SAAS,UAAU;AAC1B,eAAO;AAAA,MACT;AAAA,MACA,SAAS,SAAS,QAAQ,IAAI;AAC5B,YAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,iBAAS,MAAM,GAAG,gBAAgB,cAAc,MAAM,cAAc,QAAQ,OAAO;AACjF,cAAI,mBAAmB,eAAe,cAAc,GAAG,GAAG,CAAC,GACzD,OAAO,iBAAiB,CAAC,GACzB,SAAS,iBAAiB,CAAC;AAC7B,aAAG,KAAK,SAAS,QAAQ,MAAM,YAAY;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,KAAK,SAAS,IAAIC,MAAK;AACrB,YAAI,OAAO,aAAa,OAAO,SAAU,OAAO;AAC9C,iBAAOA,KAAI,SAAS,MAAM,CAAC,EAAE,QAAQ,uCAAuCA,KAAI,YAAY,MAAM,CAAC,EAAE,UAAU;AAAA,QACjH,CAAC,EAAE,CAAC;AACJ,eAAO,QAAQ,KAAK,CAAC;AAAA,MACvB;AAAA,MACA,KAAK,SAAS,IAAIA,MAAK;AACrB,eAAO,CAAC,CAAC,eAAe,IAAIA,IAAG;AAAA,MACjC;AAAA,MACA,MAAM,SAASC,QAAO;AACpB,eAAO,aAAa,IAAI,SAAU,MAAM;AACtC,cAAI,QAAQ,eAAe,MAAM,CAAC,GAChCD,OAAM,MAAM,CAAC;AACf,iBAAOA;AAAA,QACT,CAAC;AAAA,MACH;AAAA,MACA,QAAQ,SAAS,SAAS;AACxB,eAAO,aAAa,IAAI,SAAU,OAAO;AACvC,cAAI,QAAQ,eAAe,OAAO,CAAC,GACjCE,UAAS,MAAM,CAAC;AAClB,iBAAOA;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AACA,aAAS,8BAA8B,GAAG,GAAG;AAC3C,aAAO,EAAE,SAAS,EAAE,QAAQ,kCAAkC,EAAE,aAAa,EAAE,WAAW,KAAK,uCAAuC,EAAE,YAAY,EAAE,UAAU;AAAA,IAClK;AACA,aAAS,kCAAkC,GAAG,GAAG;AAC/C,UAAI,MAAM,UAAa,MAAM,QAAW;AACtC,eAAO;AAAA,MACT;AACA,UAAI,MAAM,UAAa,MAAM,QAAW;AACtC,eAAO;AAAA,MACT;AACA,UAAI,MAAM,UAAa,MAAM,QAAW;AACtC,YAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,iBAAO;AAAA,QACT;AACA,iBAAS,MAAM,GAAG,MAAM,EAAE,QAAQ,OAAO;AACvC,cAAI,EAAE,GAAG,MAAM,EAAE,GAAG,GAAG;AACrB,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,aAAS,uCAAuC,GAAG,GAAG;AACpD,UAAI,MAAM,UAAa,MAAM,QAAW;AACtC,eAAO;AAAA,MACT;AACA,UAAI,MAAM,UAAa,MAAM,QAAW;AACtC,eAAO;AAAA,MACT;AACA,UAAI,MAAM,UAAa,MAAM,QAAW;AACtC,YAAI,EAAE,WAAW,EAAE,QAAQ;AACzB,iBAAO;AAAA,QACT;AACA,iBAAS,MAAM,GAAG,MAAM,EAAE,QAAQ,OAAO;AACvC,cAAI,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,UAAU,EAAE,GAAG,EAAE,OAAO;AAChE,mBAAO;AAAA,UACT;AACA,cAAI,EAAE,GAAG,EAAE,gBAAgB,UAAa,EAAE,GAAG,EAAE,gBAAgB,QAAW;AACxE,mBAAO;AAAA,UACT;AACA,cAAI,EAAE,GAAG,EAAE,gBAAgB,UAAa,EAAE,GAAG,EAAE,gBAAgB,QAAW;AACxE,mBAAO;AAAA,UACT;AACA,cAAI,EAAE,GAAG,EAAE,gBAAgB,UAAa,EAAE,GAAG,EAAE,gBAAgB,QAAW;AACxE,gBAAI,EAAE,GAAG,EAAE,YAAY,WAAW,EAAE,GAAG,EAAE,YAAY,QAAQ;AAC3D,qBAAO;AAAA,YACT;AACA,qBAAS,IAAI,GAAG,IAAI,EAAE,GAAG,EAAE,YAAY,QAAQ,KAAK;AAClD,kBAAI,EAAE,GAAG,EAAE,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,YAAY,CAAC,GAAG;AACnD,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,QAAI,WAAW,QAAQ,WAAW,GAAG,oBAAoB,SAAS,gBAAgB,eAAe,QAAQ,CAAC;AAAA;AAAA;;;ACvJ1G;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,sBAAsB,uBAAuB,4BAAoC;AACrF,QAAI,YAAY,uBAAuB,kBAAqB;AAC5D,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,aAAS,eAAe,GAAG,GAAG;AAAE,aAAO,gBAAgB,CAAC,KAAK,sBAAsB,GAAG,CAAC,KAAK,4BAA4B,GAAG,CAAC,KAAK,iBAAiB;AAAA,IAAG;AACrJ,aAAS,mBAAmB;AAAE,YAAM,IAAI,UAAU,2IAA2I;AAAA,IAAG;AAChM,aAAS,4BAA4B,GAAG,GAAG;AAAE,UAAI,GAAG;AAAE,YAAI,YAAY,OAAO,EAAG,QAAO,kBAAkB,GAAG,CAAC;AAAG,YAAI,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,eAAO,aAAa,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,UAAU,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC,IAAI,gBAAgB,KAAK,2CAA2C,KAAK,CAAC,IAAI,kBAAkB,GAAG,CAAC,IAAI;AAAA,MAAQ;AAAA,IAAE;AACzX,aAAS,kBAAkB,GAAG,GAAG;AAAE,OAAC,QAAQ,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE;AAAS,eAAS,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC;AAAG,aAAO;AAAA,IAAG;AACnJ,aAAS,sBAAsB,GAAG,GAAG;AAAE,UAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAAG,UAAI,QAAQ,GAAG;AAAE,YAAI,GAAG,GAAGC,IAAG,GAAG,IAAI,CAAC,GAAG,IAAI,MAAI,IAAI;AAAI,YAAI;AAAE,cAAIA,MAAK,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG;AAAE,gBAAI,OAAO,CAAC,MAAM,EAAG;AAAQ,gBAAI;AAAA,UAAI,MAAO,QAAO,EAAE,KAAK,IAAIA,GAAE,KAAK,CAAC,GAAG,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,WAAW,IAAI,IAAI,KAAG;AAAA,QAAE,SAASC,IAAG;AAAE,cAAI,MAAI,IAAIA;AAAA,QAAG,UAAE;AAAU,cAAI;AAAE,gBAAI,CAAC,KAAK,QAAQ,EAAE,WAAW,IAAI,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,GAAI;AAAA,UAAQ,UAAE;AAAU,gBAAI,EAAG,OAAM;AAAA,UAAG;AAAA,QAAE;AAAE,eAAO;AAAA,MAAG;AAAA,IAAE;AACnhB,aAAS,gBAAgB,GAAG;AAAE,UAAI,MAAM,QAAQ,CAAC,EAAG,QAAO;AAAA,IAAG;AAC9D,QAAI,cAAc,CAAC;AACnB,QAAI,OAAO,UAAU,QAAQ,KAAK;AAClC,SAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,YAAM,KAAK,CAAC;AACZ,aAAO,UAAU,QAAQ,IAAI,GAAG;AAChC,yBAAmB,CAAC;AACxB,UAAI,MAAM;AACJ,mBAAW,CAAC,EAAE,OAAO,KAAK,cAAc,KAAK,eAAe;AAChE,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACpC,qBAAW,SAAS,CAAC;AACzB,cAAI,SAAS,WAAW,QAAQ;AAC1B,sBAAU,SAAS;AACvB,gBAAI,WAAW,MAAM;AACnB,+BAAiB,KAAK,OAAO;AAAA,YAC/B;AAAA,UACF;AAAA,QACF;AACA,YAAI,iBAAiB,SAAS,GAAG;AAC/B,sBAAY,KAAK,CAAC,KAAK,gBAAgB,CAAC;AAAA,QAC1C;AAAA,MACF;AAAA,IACF;AAlBM;AACA;AACA;AAEE;AAEE;AAEE;AAHC;AANJ;AAoBT,QAAI,iBAAiB;AAAA,MACnB,SAAS,SAAS,UAAU;AAC1B,eAAO;AAAA,MACT;AAAA,MACA,SAAS,SAAS,QAAQ,IAAI;AAC5B,YAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,iBAAS,KAAK,GAAG,eAAe,aAAa,KAAK,aAAa,QAAQ,MAAM;AAC3E,cAAI,kBAAkB,eAAe,aAAa,EAAE,GAAG,CAAC,GACtD,OAAO,gBAAgB,CAAC,GACxB,SAAS,gBAAgB,CAAC;AAC5B,aAAG,KAAK,SAAS,QAAQ,MAAM,WAAW;AAAA,QAC5C;AAAA,MACF;AAAA,MACA,KAAK,SAAS,IAAIC,MAAK;AACrB,YAAI,OAAO,YAAY,OAAO,SAAU,OAAO;AAC7C,iBAAO,MAAM,CAAC,MAAMA,OAAM,OAAO;AAAA,QACnC,CAAC,EAAE,CAAC;AACJ,eAAO,QAAQ,KAAK,CAAC;AAAA,MACvB;AAAA,MACA,KAAK,SAAS,IAAIA,MAAK;AACrB,eAAO,CAAC,CAAC,eAAe,IAAIA,IAAG;AAAA,MACjC;AAAA,MACA,MAAM,SAASC,QAAO;AACpB,eAAO,YAAY,IAAI,SAAU,MAAM;AACrC,cAAI,QAAQ,eAAe,MAAM,CAAC,GAChCD,OAAM,MAAM,CAAC;AACf,iBAAOA;AAAA,QACT,CAAC;AAAA,MACH;AAAA,MACA,QAAQ,SAAS,SAAS;AACxB,eAAO,YAAY,IAAI,SAAU,OAAO;AACtC,cAAI,QAAQ,eAAe,OAAO,CAAC,GACjCE,UAAS,MAAM,CAAC;AAClB,iBAAOA;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,WAAW,QAAQ,WAAW,GAAG,oBAAoB,SAAS,gBAAgB,eAAe,QAAQ,CAAC;AAAA;AAAA;;;AC1E1G;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,QAAQ,QAAQ,eAAe,QAAQ,eAAe,QAAQ,MAAM,QAAQ,OAAO;AAC3F,QAAI,gBAAgB,uBAAuB,sBAAyB;AACpE,QAAI,UAAU,uBAAuB,gBAAmB;AACxD,QAAI,YAAY,uBAAuB,kBAAqB;AAC5D,QAAI,kBAAkB,uBAAuB,wBAA2B;AACxE,QAAI,kBAAkB,uBAAuB,wBAA2B;AACxE,aAAS,uBAAuB,GAAG;AAAE,aAAO,KAAK,EAAE,aAAa,IAAI,EAAE,SAAS,EAAE;AAAA,IAAG;AACpF,QAAI,OAAO,QAAQ,OAAO,cAAc;AACxC,QAAI,MAAM,QAAQ,MAAM,QAAQ;AAChC,QAAI,QAAQ,QAAQ,QAAQ,UAAU;AACtC,QAAI,eAAe,QAAQ,eAAe,gBAAgB;AAC1D,QAAI,eAAe,QAAQ,eAAe,gBAAgB;AAAA;AAAA;", "names": ["o", "r", "values", "r", "values", "F", "e", "r", "_loop", "values", "i", "r", "_loop", "relation", "key", "keys", "values", "i", "r", "key", "keys", "values"]}