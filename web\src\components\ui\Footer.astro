---
// Footer principal con enlaces y información legal
// Audioguías Murales Santa Marta

export interface Props {
  lang?: string;
}

const { lang = "es" } = Astro.props;

// Contenido bilingüe
const content = {
  es: {
    title: "Audioguías Murales Santa Marta",
    description: "Descubre el arte urbano de Santa Marta con nuestras audioguías accesibles e interactivas.",
    quickLinks: "Enlaces rápidos",
    home: "Inicio",
    audioguides: "Audioguías",
    normative: "Normativa",
    descriptive: "Descriptiva",
    easy: "Fácil",
    sign: "Signoguía",
    map: "Mapa de ruta",
    download: "Descargar PDF",
    accessibility: "Accesibilidad",
    accessibilityFeatures: "Características de accesibilidad",
    highContrast: "Alto contraste",
    fontSize: "Tamaño de fuente ajustable",
    keyboardNav: "Navegación por teclado",
    screenReader: "Compatible con lectores de pantalla",
    reducedMotion: "Respeta preferencias de movimiento",
    contact: "Contacto",
    email: "<EMAIL>",
    phone: "+34 923 XXX XXX",
    address: "Santa Marta de Tormes, Salamanca",
    legal: "Legal",
    privacy: "Política de privacidad",
    terms: "Términos de uso",
    cookies: "Política de cookies",
    copyright: "© 2025 Audioguías Murales Santa Marta. Todos los derechos reservados.",
    poweredBy: "Desarrollado con",
    madeWith: "Hecho con ❤️ para la accesibilidad",
    wcagCompliant: "Cumple WCAG 2.1 AA"
  },
  en: {
    title: "Santa Marta Murals Audio Guides",
    description: "Discover Santa Marta's urban art with our accessible and interactive audio guides.",
    quickLinks: "Quick links",
    home: "Home",
    audioguides: "Audio Guides",
    normative: "Standard",
    descriptive: "Descriptive",
    easy: "Easy",
    sign: "Sign Guide",
    map: "Route map",
    download: "Download PDF",
    accessibility: "Accessibility",
    accessibilityFeatures: "Accessibility features",
    highContrast: "High contrast",
    fontSize: "Adjustable font size",
    keyboardNav: "Keyboard navigation",
    screenReader: "Screen reader compatible",
    reducedMotion: "Respects motion preferences",
    contact: "Contact",
    email: "<EMAIL>",
    phone: "+34 923 XXX XXX",
    address: "Santa Marta de Tormes, Salamanca",
    legal: "Legal",
    privacy: "Privacy policy",
    terms: "Terms of use",
    cookies: "Cookie policy",
    copyright: "© 2025 Santa Marta Murals Audio Guides. All rights reserved.",
    poweredBy: "Built with",
    madeWith: "Made with ❤️ for accessibility",
    wcagCompliant: "WCAG 2.1 AA Compliant"
  }
};

const t = content[lang as keyof typeof content];
---

<footer class="bg-white dark:bg-slate-900 border-t border-slate-200 dark:border-slate-700 mt-auto">
  <div class="container mx-auto px-4 py-8 md:py-12">
    
    <!-- Contenido principal del footer -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
      
      <!-- Información principal -->
      <div class="lg:col-span-2">
        <div class="mb-4">
          <h2 class="text-xl font-bold mb-2">
            <span class="text-SM-blue">AUDIOGUÍAS</span>
            <span class="text-SM-yellow">MURALES</span>
            <span class="block text-base font-normal text-SM-gray">Santa Marta</span>
          </h2>
        </div>
        <p class="text-slate-600 dark:text-slate-300 text-sm leading-relaxed mb-4 max-w-md">
          {t.description}
        </p>
        
        <!-- Información de contacto -->
        <div class="space-y-2 text-sm">
          <div class="flex items-center text-slate-600 dark:text-slate-300">
            <svg class="w-4 h-4 mr-2 text-SM-blue" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
            <a href={`mailto:${t.email}`} class="hover:text-SM-blue transition-colors">
              {t.email}
            </a>
          </div>
          <div class="flex items-center text-slate-600 dark:text-slate-300">
            <svg class="w-4 h-4 mr-2 text-SM-blue" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
            </svg>
            <a href={`tel:${t.phone.replace(/\s/g, '')}`} class="hover:text-SM-blue transition-colors">
              {t.phone}
            </a>
          </div>
          <div class="flex items-center text-slate-600 dark:text-slate-300">
            <svg class="w-4 h-4 mr-2 text-SM-blue" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
            </svg>
            <span>{t.address}</span>
          </div>
        </div>
      </div>
      
      <!-- Enlaces rápidos -->
      <div>
        <h3 class="font-semibold text-slate-900 dark:text-slate-100 mb-4">{t.quickLinks}</h3>
        <ul class="space-y-2 text-sm">
          <li>
            <a href={lang === "es" ? "/" : "/english"} class="text-slate-600 dark:text-slate-300 hover:text-SM-blue transition-colors">
              {t.home}
            </a>
          </li>
          <li>
            <a href="/audioguia-normativa" class="text-slate-600 dark:text-slate-300 hover:text-SM-blue transition-colors">
              {t.normative}
            </a>
          </li>
          <li>
            <a href="/audioguia-descriptiva" class="text-slate-600 dark:text-slate-300 hover:text-SM-blue transition-colors">
              {t.descriptive}
            </a>
          </li>
          <li>
            <a href="/audioguia-facil" class="text-slate-600 dark:text-slate-300 hover:text-SM-blue transition-colors">
              {t.easy}
            </a>
          </li>
          <li>
            <a href="/signoguia" class="text-slate-600 dark:text-slate-300 hover:text-SM-blue transition-colors">
              {t.sign}
            </a>
          </li>
          <li>
            <a href="#mapa" class="text-slate-600 dark:text-slate-300 hover:text-SM-blue transition-colors">
              {t.map}
            </a>
          </li>
        </ul>
      </div>
      
      <!-- Accesibilidad -->
      <div>
        <h3 class="font-semibold text-slate-900 dark:text-slate-100 mb-4">{t.accessibility}</h3>
        <ul class="space-y-2 text-sm text-slate-600 dark:text-slate-300">
          <li class="flex items-center">
            <svg class="w-3 h-3 mr-2 text-green-500" fill="currentColor">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            {t.highContrast}
          </li>
          <li class="flex items-center">
            <svg class="w-3 h-3 mr-2 text-green-500" fill="currentColor">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            {t.fontSize}
          </li>
          <li class="flex items-center">
            <svg class="w-3 h-3 mr-2 text-green-500" fill="currentColor">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            {t.keyboardNav}
          </li>
          <li class="flex items-center">
            <svg class="w-3 h-3 mr-2 text-green-500" fill="currentColor">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            {t.screenReader}
          </li>
        </ul>
        
        <!-- Badge de cumplimiento WCAG -->
        <div class="mt-4">
          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200">
            <svg class="w-3 h-3 mr-1" fill="currentColor">
              <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
            </svg>
            {t.wcagCompliant}
          </span>
        </div>
      </div>
    </div>
    
    <!-- Separador -->
    <div class="border-t border-slate-200 dark:border-slate-700 pt-6">
      <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
        
        <!-- Copyright -->
        <div class="text-sm text-slate-600 dark:text-slate-300 text-center md:text-left">
          {t.copyright}
        </div>
        
        <!-- Enlaces legales -->
        <div class="flex flex-wrap justify-center md:justify-end space-x-4 text-sm">
          <a href="/privacidad" class="text-slate-600 dark:text-slate-300 hover:text-SM-blue transition-colors">
            {t.privacy}
          </a>
          <a href="/terminos" class="text-slate-600 dark:text-slate-300 hover:text-SM-blue transition-colors">
            {t.terms}
          </a>
          <a href="/cookies" class="text-slate-600 dark:text-slate-300 hover:text-SM-blue transition-colors">
            {t.cookies}
          </a>
        </div>
      </div>
      
      <!-- Información técnica -->
      <div class="mt-4 text-center">
        <p class="text-xs text-slate-500 dark:text-slate-400">
          {t.poweredBy} <span class="text-SM-blue font-medium">Astro</span> + 
          <span class="text-SM-blue font-medium">Tailwind CSS</span> • 
          {t.madeWith}
        </p>
      </div>
    </div>
  </div>
</footer>
