import { useState, useEffect } from 'react';

const AudioPlayer = ({
  currentMural,
  onNext,
  onPrevious,
  audioType = 'normal',
  language = 'es',
  className = ""
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Obtener URL del audio según tipo y idioma
  const getAudioUrl = (mural) => {
    if (!mural || !mural.audio) return null;

    let audioUrl;
    if (audioType === 'normal') {
      audioUrl = mural.audio.normal?.[language] || mural.audio.normal?.es;
    } else {
      audioUrl = mural.audio[audioType];
    }

    return audioUrl;
  };

  // Cargar audio
  useEffect(() => {
    if (currentMural) {
      setIsLoading(true);
      setError(null);

      setTimeout(() => {
        setIsLoading(false);
      }, 500);
    }
  }, [currentMural, audioType]);

  if (!currentMural) {
    return (
      <div className={`bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-slate-200 dark:border-slate-700 ${className}`}>
        <div className="text-center py-12">
          <div className="text-6xl mb-4 animate-pulse">🎨</div>
          <h3 className="font-bold text-xl mb-3 text-slate-900 dark:text-slate-100">
            ¡Descubre los Murales!
          </h3>
          <p className="text-slate-600 dark:text-slate-400 mb-6">
            Haz clic en "Comenzar Tour" para explorar el arte urbano de Santa Marta
          </p>
          <div className="flex items-center justify-center space-x-2 text-sm text-slate-500 dark:text-slate-400">
            <span>👆</span>
            <span>Usa el botón azul de arriba</span>
          </div>
        </div>
      </div>
    );
  }

  const audioUrl = getAudioUrl(currentMural);

  return (
    <div className={`bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 overflow-hidden ${className}`}>
      {/* Header con información del mural */}
      <div className="p-6">
        <div className="flex items-start space-x-4">
          {/* Cover del mural */}
          <div className="flex-shrink-0">
            <div className="w-20 h-20 rounded-lg overflow-hidden bg-gradient-to-br from-SM-blue to-blue-700 flex items-center justify-center">
              {currentMural.image ? (
                <img
                  src={currentMural.image}
                  alt={currentMural.alt?.[language] || currentMural.title[language]}
                  className="w-full h-full object-cover"
                />
              ) : (
                <span className="text-white text-2xl">🎨</span>
              )}
            </div>
          </div>

          {/* Información del mural */}
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg text-slate-900 dark:text-slate-100 truncate">
              {currentMural.title[language]}
            </h3>
            <p className="text-slate-600 dark:text-slate-400 text-sm mb-2">
              Audioguía {audioType === 'normal' ? 'Normativa' :
                        audioType === 'descriptive' ? 'Descriptiva' :
                        audioType === 'easy' ? 'Fácil' : 'Signoguía'}
            </p>
            <p className="text-xs text-slate-500 dark:text-slate-400">
              Mural {currentMural.order} • {currentMural.description[language]}
            </p>
          </div>

          {/* Botones de navegación */}
          <div className="flex items-center space-x-2">
            <button
              onClick={onPrevious}
              disabled={!onPrevious}
              className="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Mural anterior"
            >
              <svg className="w-5 h-5 text-slate-600 dark:text-slate-300" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7"/>
              </svg>
            </button>

            <button
              onClick={onNext}
              disabled={!onNext}
              className="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              aria-label="Mural siguiente"
            >
              <svg className="w-5 h-5 text-slate-600 dark:text-slate-300" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Reproductor de Audio.com */}
      {audioUrl && (
        <div className="px-6 pb-6">
          <div className="bg-slate-50 dark:bg-slate-700 rounded-xl p-6">
            <div className="text-center">
              <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-4">
                🎧 Reproductor de Audio
              </h4>

              {/* Embed de Audio.com con el formato exacto */}
              <div style={{ height: '228px', width: '204px', margin: '0 auto' }}>
                <iframe
                  id={`audio-iframe-${currentMural.id}`}
                  src={audioUrl}
                  style={{
                    display: 'block',
                    borderRadius: '1px',
                    border: 'none',
                    height: '204px',
                    width: '204px'
                  }}
                  allow="autoplay"
                  title={`Audio: ${currentMural.title[language]}`}
                />
                <a
                  href="https://audio.com/jorge-badillo"
                  style={{
                    textAlign: 'center',
                    display: 'block',
                    color: '#A4ABB6',
                    fontSize: '12px',
                    fontFamily: 'sans-serif',
                    lineHeight: '16px',
                    marginTop: '8px',
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis'
                  }}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  @jorge-badillo
                </a>
              </div>

              <p className="text-xs text-slate-500 dark:text-slate-400 mt-4">
                Usa los controles del reproductor para escuchar la audioguía
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Estado de carga */}
      {isLoading && (
        <div className="px-6 pb-6">
          <div className="flex items-center justify-center space-x-2 text-slate-600 dark:text-slate-400">
            <div className="animate-spin w-4 h-4 border-2 border-SM-blue border-t-transparent rounded-full"></div>
            <span className="text-sm">Cargando audio...</span>
          </div>
        </div>
      )}

      {/* Estado de error */}
      {error && (
        <div className="px-6 pb-6">
          <div className="bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-200 rounded-lg p-3 text-sm">
            <div className="flex items-center">
              <span className="mr-2">⚠️</span>
              <span>{error}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AudioPlayer;
