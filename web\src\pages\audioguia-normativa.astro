---
import Layout from '../components/ui/Layout.astro';
import Header from '../components/ui/Header.astro';
import Footer from '../components/ui/Footer.astro';
import AudioguideContainer from '../components/react/AudioguideContainer.jsx';

// Importar datos
import muralsData from '../data/murals.json';
import contentEs from '../data/content-es.json';

// Configuración de la página
const lang = 'es';
const audioType = 'normal';
const { murals, route } = muralsData;
const content = contentEs;

// SEO y metadatos
const pageTitle = `${content.audioguide_types.normal.title} | Audioguías Murales Santa Marta`;
const pageDescription = content.audioguide_types.normal.description;
---

<Layout 
  title={pageTitle}
  description={pageDescription}
  lang={lang}
>
  <Header lang={lang} />
  
  <main class="min-h-screen bg-slate-50 dark:bg-slate-900">
    <!-- Skip link -->
    <a href="#main-content" class="skip-link">
      {content.accessibility.skipToContent}
    </a>

    <!-- Hero section simplificado y directo -->
    <section class="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700">
      <div class="container mx-auto px-4 py-6">
        <div class="max-w-4xl mx-auto text-center">
          <!-- Breadcrumb -->
          <nav class="mb-4" aria-label="Breadcrumb">
            <ol class="flex items-center justify-center space-x-2 text-sm">
              <li>
                <a href="/" class="text-SM-blue hover:text-blue-700 transition-colors">
                  {content.navigation.home}
                </a>
              </li>
              <li class="text-slate-400">/</li>
              <li class="text-slate-600 dark:text-slate-400" aria-current="page">
                {content.audioguide_types.normal.title}
              </li>
            </ol>
          </nav>

          <!-- Título directo -->
          <h1 class="text-fluid-xl font-bold mb-6 text-slate-900 dark:text-slate-100">
            🎧 {content.audioguide_types.normal.title}
          </h1>

          <!-- Botón principal súper prominente -->
          <div class="mb-8">
            <button
              id="start-tour-btn"
              onclick="window.startAudioguide && window.startAudioguide()"
              class="group relative bg-gradient-to-r from-SM-blue to-blue-700 hover:from-blue-700 hover:to-SM-blue text-white font-bold text-xl px-12 py-6 rounded-2xl shadow-2xl transition-all duration-300 hover:shadow-3xl hover:scale-110 focus:outline-none focus:ring-4 focus:ring-SM-blue/50 transform hover:-translate-y-1"
            >
              <span class="flex items-center justify-center space-x-3">
                <span class="text-2xl group-hover:animate-pulse">🎨</span>
                <span>Comenzar Tour de Murales</span>
                <span class="text-2xl group-hover:animate-bounce">▶️</span>
              </span>

              <!-- Efecto de brillo -->
              <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12"></div>
            </button>

            <!-- Texto de ayuda sutil -->
            <p class="text-sm text-slate-500 dark:text-slate-400 mt-3">
              👆 Haz clic para comenzar tu experiencia inmersiva
            </p>
          </div>

          <!-- Información básica compacta -->
          <div class="flex flex-wrap items-center justify-center gap-3 text-sm text-slate-600 dark:text-slate-400">
            <span class="flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              {content.audioguide_types.normal.duration}
            </span>
            <span class="text-slate-400">•</span>
            <span class="flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
              </svg>
              {murals.length} murales
            </span>
            <span class="text-slate-400">•</span>
            <span>Español e Inglés</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Contenido principal -->
    <div id="main-content" class="container mx-auto px-4 py-8">
      <div class="max-w-6xl mx-auto">

        <!-- Componente contenedor con toda la funcionalidad -->
        <AudioguideContainer
          muralsData={murals}
          routeData={route}
          audioType={audioType}
          language={lang}
          client:load
        />
      </div>
    </div>
  </main>

  <Footer lang={lang} />

  <!-- Script para funcionalidad básica -->
  <script>
    // El componente AudioguideContainer maneja toda la funcionalidad
    // Solo necesitamos asegurar que el botón de inicio funcione
    document.addEventListener('DOMContentLoaded', () => {
      // El botón ya tiene onclick="window.startAudioguide && window.startAudioguide()"
      // La función startAudioguide se expone desde AudioguideContainer
    });
  </script>
</Layout>
