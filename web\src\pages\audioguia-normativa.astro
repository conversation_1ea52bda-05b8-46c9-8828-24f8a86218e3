---
import Layout from '../components/ui/Layout.astro';
import Header from '../components/ui/Header.astro';
import Footer from '../components/ui/Footer.astro';
import AudioguideContainer from '../components/react/AudioguideContainer.jsx';

// Importar datos
import muralsData from '../data/murals.json';
import contentEs from '../data/content-es.json';

// Configuración de la página
const lang = 'es';
const audioType = 'normal';
const { murals, route } = muralsData;
const content = contentEs;

// SEO y metadatos
const pageTitle = `${content.audioguide_types.normal.title} | Audioguías Murales Santa Marta`;
const pageDescription = content.audioguide_types.normal.description;
---

<Layout 
  title={pageTitle}
  description={pageDescription}
  lang={lang}
>
  <Header lang={lang} />
  
  <main class="min-h-screen bg-slate-50 dark:bg-slate-900">
    <!-- Skip link -->
    <a href="#main-content" class="skip-link">
      {content.accessibility.skipToContent}
    </a>

    <!-- Hero section con información de la audioguía -->
    <section class="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700">
      <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto text-center">
          <!-- Breadcrumb -->
          <nav class="mb-6" aria-label="Breadcrumb">
            <ol class="flex items-center justify-center space-x-2 text-sm">
              <li>
                <a href="/" class="text-SM-blue hover:text-blue-700 transition-colors">
                  {content.navigation.home}
                </a>
              </li>
              <li class="text-slate-400">/</li>
              <li class="text-slate-600 dark:text-slate-400" aria-current="page">
                {content.audioguide_types.normal.title}
              </li>
            </ol>
          </nav>

          <!-- Título y descripción -->
          <div class="mb-8">
            <h1 class="text-fluid-xl font-bold mb-4 text-slate-900 dark:text-slate-100">
              🎧 {content.audioguide_types.normal.title}
            </h1>
            <p class="text-fluid-base text-slate-600 dark:text-slate-300 max-w-3xl mx-auto mb-6">
              {content.audioguide_types.normal.description}
            </p>
            
            <!-- Características de la audioguía -->
            <div class="flex flex-wrap items-center justify-center gap-4 text-sm">
              <div class="flex items-center bg-SM-blue/10 dark:bg-SM-blue/20 text-SM-blue px-3 py-1 rounded-full">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                {content.audioguide_types.normal.duration}
              </div>
              <div class="flex items-center bg-SM-yellow/10 dark:bg-SM-yellow/20 text-SM-yellow px-3 py-1 rounded-full">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                </svg>
                {murals.length} murales
              </div>
              <div class="flex items-center bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-3 py-1 rounded-full">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                </svg>
                Español e Inglés
              </div>
            </div>
          </div>

          <!-- Botones de acción -->
          <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
            <button
              id="start-tour-btn"
              onclick="window.startAudioguide && window.startAudioguide()"
              class="bg-SM-blue hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-SM-blue focus:ring-offset-2 dark:focus:ring-offset-slate-800"
            >
              🎧 Comenzar audioguía
            </button>
            <a
              href="#mapa"
              class="border-2 border-SM-blue text-SM-blue hover:bg-blue-50 dark:hover:bg-blue-900/20 font-medium px-6 py-3 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-SM-blue focus:ring-offset-2 dark:focus:ring-offset-slate-800"
            >
              🗺️ Ver mapa
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Contenido principal -->
    <div id="main-content" class="container mx-auto px-4 py-8">
      <div class="max-w-6xl mx-auto">

        <!-- Componente contenedor con toda la funcionalidad -->
        <AudioguideContainer
          muralsData={murals}
          routeData={route}
          audioType={audioType}
          language={lang}
          client:load
        />

        <!-- Información adicional -->
        <section class="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border border-slate-200 dark:border-slate-700">
          <h2 class="font-semibold text-lg mb-4 text-slate-900 dark:text-slate-100">
            ℹ️ Información de la audioguía
          </h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Características -->
            <div>
              <h3 class="font-medium text-slate-900 dark:text-slate-100 mb-3">
                ✨ Características
              </h3>
              <ul class="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                {content.audioguide_types.normal.features.map((feature: string) => (
                  <li class="flex items-start">
                    <span class="text-SM-blue mr-2 mt-0.5">•</span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            <!-- Instrucciones -->
            <div>
              <h3 class="font-medium text-slate-900 dark:text-slate-100 mb-3">
                📋 Instrucciones
              </h3>
              <ol class="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                <li class="flex items-start">
                  <span class="text-SM-blue mr-2 mt-0.5 font-medium">1.</span>
                  Selecciona un mural en el mapa o la lista
                </li>
                <li class="flex items-start">
                  <span class="text-SM-blue mr-2 mt-0.5 font-medium">2.</span>
                  Usa los controles para reproducir el audio
                </li>
                <li class="flex items-start">
                  <span class="text-SM-blue mr-2 mt-0.5 font-medium">3.</span>
                  Sigue la ruta recomendada para la mejor experiencia
                </li>
                <li class="flex items-start">
                  <span class="text-SM-blue mr-2 mt-0.5 font-medium">4.</span>
                  Usa auriculares para una mejor calidad de audio
                </li>
              </ol>
            </div>
          </div>

          <!-- Consejos de accesibilidad -->
          <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
            <h3 class="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center">
              ♿ Accesibilidad
            </h3>
            <p class="text-sm text-blue-700 dark:text-blue-300">
              Esta audioguía es compatible con lectores de pantalla y navegación por teclado. 
              Usa las teclas de flecha para navegar entre controles y la barra espaciadora para reproducir/pausar.
            </p>
          </div>
        </section>
      </div>
    </div>
  </main>

  <Footer lang={lang} />

  <!-- Script para funcionalidad básica -->
  <script>
    // El componente AudioguideContainer maneja toda la funcionalidad
    // Solo necesitamos asegurar que el botón de inicio funcione
    document.addEventListener('DOMContentLoaded', () => {
      // El botón ya tiene onclick="window.startAudioguide && window.startAudioguide()"
      // La función startAudioguide se expone desde AudioguideContainer
    });
  </script>
</Layout>
