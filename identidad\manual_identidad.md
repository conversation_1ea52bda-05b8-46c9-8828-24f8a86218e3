# Manual de Identidad Web para Audioguías Murales Santa Marta

Este manual contiene las directrices para la implementación correcta de la identidad de marca del proyecto "Audioguías Murales Santa Marta" en el desarrollo web utilizando Astro 5, React 19 y Tailwind CSS 4, con especial énfasis en accesibilidad y diseño responsive.

## Índice
1. [Introducción](#introducción)
2. [Concepto e Inspiración](#concepto-e-inspiración)
3. [Paleta de Colores](#paleta-de-colores)
4. [Tipografía](#tipografía)
5. [Logotipo e Identidad Visual](#logotipo-e-identidad-visual)
6. [Elementos Visuales](#elementos-visuales)
7. [Configuración de Tailwind](#configuración-de-tailwind)
8. [Componentes UI](#componentes-ui)
9. [Accesibilidad](#accesibilidad)
10. [Usos Incorrectos](#usos-incorrectos)

## Introducción

La identidad visual del proyecto "Audioguías Murales Santa Marta" debe reflejar la esencia artística y cultural del arte urbano mientras mantiene la funcionalidad, accesibilidad y usabilidad necesarias para una experiencia turística digital de calidad.

La correcta aplicación de estas normas es fundamental para:
- Conectar la experiencia digital con el arte físico de los murales
- Crear una atmósfera cultural y artística coherente
- Garantizar máxima accesibilidad para todos los usuarios
- Establecer una identidad reconocible para el turismo cultural de Santa Marta

## Concepto e Inspiración

### Filosofía de Diseño
**"La llegada del color digital"** - Inspirado en el proyecto "La llegada del color" de Daniel Martín en Santa Marta de Tormes, nuestra identidad digital busca traducir la expresividad y vitalidad del arte mural al espacio web.

### Elementos Conceptuales
- **Arte urbano como inspiración** - Colores vibrantes y contrastes marcados
- **Accesibilidad universal** - El arte debe ser para todos
- **Conexión territorio-digital** - Vincular la experiencia física con la digital
- **Herencia cultural** - Respeto por la tradición castellana y el arte contemporáneo

## Paleta de Colores

### Colores Principales

| Color | Nombre | HEX | RGB | CMYK | Variable Tailwind |
|-------|--------|-----|-----|------|-------------------|
| ![Azul SM](https://via.placeholder.com/20/0072c0/FFFFFF?text=+) | Azul SM | #0072c0 | 0, 114, 192 | C:100 M:41 Y:0 K:25 | `SM-blue` |
| ![Amarillo SM](https://via.placeholder.com/20/f8c200/000000?text=+) | Amarillo SM | #f8c200 | 248, 194, 0 | C:0 M:22 Y:100 K:3 | `SM-yellow` |
| ![Gris SM](https://via.placeholder.com/20/c3c5c5/000000?text=+) | Gris SM | #c3c5c5 | 195, 197, 197 | C:1 M:0 Y:0 K:23 | `SM-gray` |
| ![Negro SM](https://via.placeholder.com/20/282828/FFFFFF?text=+) | Negro SM | #282828 | 40, 40, 40 | C:0 M:0 Y:0 K:84 | `SM-black` |

### Esquema de colores para modo claro

| Uso | Color | Variable Tailwind |
|-----|-------|-------------------|
| Fondo principal | `#FEFEFE` | `bg-slate-50` |
| Fondo secundario | `#F8FAFC` | `bg-slate-100` |
| Texto principal | `#0F172A` | `text-slate-900` |
| Texto secundario | `#475569` | `text-slate-600` |
| Texto terciario | `#64748B` | `text-slate-500` |
| Bordes | `#E2E8F0` | `border-slate-200` |
| Acentos principales | `#0072c0` | `bg-SM-blue` |
| Acentos secundarios | `#f8c200` | `bg-SM-yellow` |
| Elementos neutros | `#c3c5c5` | `bg-SM-gray` |
| Contraste fuerte | `#282828` | `bg-SM-black` |
| Advertencia | `#D97706` | `bg-amber-600` |
| Error | `#DC2626` | `bg-red-600` |

### Esquema de colores para modo oscuro

| Uso | Color | Variable Tailwind |
|-----|-------|-------------------|
| Fondo principal | `#0F172A` | `dark:bg-slate-900` |
| Fondo secundario | `#1E293B` | `dark:bg-slate-800` |
| Fondo terciario | `#334155` | `dark:bg-slate-700` |
| Texto principal | `#F1F5F9` | `dark:text-slate-100` |
| Texto secundario | `#CBD5E1` | `dark:text-slate-300` |
| Texto terciario | `#94A3B8` | `dark:text-slate-400` |
| Bordes | `#475569` | `dark:border-slate-600` |
| Acentos principales | `#0072c0` | `dark:bg-SM-blue` |
| Acentos secundarios | `#f8c200` | `dark:bg-SM-yellow` |
| Elementos neutros | `#c3c5c5` | `dark:bg-SM-gray` |

### Accesibilidad Cromática
- **Contraste mínimo:** WCAG 2.1 AA (4.5:1 para texto normal, 3:1 para texto grande)
- **Alto contraste:** Modo disponible con ratios 7:1+
- **Sin dependencia del color:** Toda información transmitida también por forma/texto

## Tipografía

### Familia Tipográfica Principal
**Nunito** - Fuente sans-serif moderna, legible y amigable que refleja la accesibilidad y calidez del proyecto.

#### Características de Nunito
- **Legibilidad excepcional** en pantallas de todos los tamaños
- **Formas redondeadas** que transmiten calidez y accesibilidad
- **Amplio rango de pesos** disponibles (200-900)
- **Soporte completo** para caracteres latinos y acentos españoles
- **Optimizada para web** con carga rápida y renderizado suave


### Jerarquía Tipográfica

| Elemento | Peso | Tamaño | Line Height | Clase Tailwind |
|----------|------|--------|-------------|----------------|
| H1 - Título Principal | Bold (700) | 36px | 1.2 | `font-bold text-4xl leading-tight` |
| H2 - Título Sección | Semibold (600) | 30px | 1.3 | `font-semibold text-3xl leading-snug` |
| H3 - Subtítulo | Semibold (600) | 24px | 1.4 | `font-semibold text-2xl leading-relaxed` |
| H4 - Título Card | Medium (500) | 20px | 1.4 | `font-medium text-xl leading-relaxed` |
| H5 - Título Menor | Medium (500) | 18px | 1.4 | `font-medium text-lg leading-relaxed` |
| Párrafo Normal | Regular (400) | 16px | 1.6 | `font-normal text-base leading-relaxed` |
| Párrafo Pequeño | Regular (400) | 14px | 1.6 | `font-normal text-sm leading-relaxed` |
| Botones | Medium (500) | 16px | 1.4 | `font-medium text-base leading-relaxed` |
| Caption/Meta | Regular (400) | 12px | 1.4 | `font-normal text-xs leading-relaxed` |

### Configuración de Fuentes Responsiva

```css
/* Escalado tipográfico responsive */
.text-fluid-xl { font-size: clamp(1.75rem, 4vw, 2.25rem); }
.text-fluid-lg { font-size: clamp(1.5rem, 3vw, 1.875rem); }
.text-fluid-base { font-size: clamp(1rem, 2vw, 1.125rem); }
.text-fluid-sm { font-size: clamp(0.875rem, 1.5vw, 1rem); }
```

## Logotipo e Identidad Visual

### Concepto del Logo
El logotipo debe integrar elementos que representen:
- **Audio/Sonido** - Ondas sonoras, iconos de audio
- **Murales/Arte** - Pincel, paleta, formas artísticas
- **Localización** - Referencia sutil a Santa Marta/Tormes
- **Digital** - Elementos que sugieran tecnología accesible

### Variantes de Logo

#### 1. **Logo Principal**
- Texto "AUDIOGUÍAS" en azul SM (#0072c0)
- Texto "MURALES" en amarillo SM (#f8c200)
- Subtítulo "Santa Marta" en gris SM (#c3c5c5)
- Icono de audio integrado

#### 2. **Logo Horizontal**
- Para headers y espacios anchos
- Elementos dispuestos en línea horizontal
- Altura mínima: 40px

#### 3. **Logo Vertical**
- Para espacios cuadrados y móviles
- Elementos apilados verticalmente
- Ancho mínimo: 120px

#### 4. **Versión Monocromática**
- Para fondos de color o situaciones especiales
- Disponible en blanco y negro
- Mantiene jerarquía visual

### Implementación en Web

```html
<!-- Logo principal responsive -->
<div class="logo-container">
  <!-- Versión móvil (vertical) -->
  <img src="/logos/logo-vertical.svg" 
       class="h-12 block md:hidden" 
       alt="Audioguías Murales Santa Marta">
  
  <!-- Versión desktop (horizontal) -->
  <img src="/logos/logo-horizontal.svg" 
       class="h-10 hidden md:block" 
       alt="Audioguías Murales Santa Marta">
</div>

<!-- Logo adaptativo para modo oscuro -->
<div class="logo-adaptive">
  <!-- Modo claro -->
  <img src="/logos/logo-color.svg" 
       class="h-10 block dark:hidden" 
       alt="Audioguías Murales Santa Marta">
  
  <!-- Modo oscuro -->
  <img src="/logos/logo-white.svg" 
       class="h-10 hidden dark:block" 
       alt="Audioguías Murales Santa Marta">
</div>
```

### Espacio de Protección
- **Mínimo:** equivalente a la altura de una "A" del texto principal
- **Recomendado:** 1.5x la altura de la letra "A"
- **En headers:** padding mínimo de 16px en todos los lados

## Elementos Visuales

### Iconografía

#### 1. **Iconos de Audio**
- Estilo: Outline, 2px stroke
- Tamaños: 16px, 20px, 24px, 32px
- Colores: Siguen paleta principal
- Conjunto: Reproducir, pausar, anterior, siguiente, volumen, lista

```html
<!-- Ejemplo de icono consistente -->
<svg class="w-6 h-6 text-santa-marta-blue" fill="none" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
        d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1l4-4v12l-4-4H9V10z"/>
</svg>
```

#### 2. **Iconos de Accesibilidad**
- Alto contraste, tamaño de fuente, idioma
- Siempre visibles y accesibles
- Con labels descriptivos

#### 3. **Iconos de Navegación**
- Hamburguesa para menú móvil
- Flechas para navegación entre pistas
- Marcadores para mapa

### Patrones Visuales

#### 1. **Cards y Tarjetas**
- **Esquinas redondeadas:** 12px (`rounded-xl`)
- **Sombras:** Sutiles, `shadow-lg` en hover
- **Bordes:** 1px en modo claro, sin bordes en modo oscuro
- **Padding:** 24px en desktop, 16px en móvil

#### 2. **Gradientes**
- **Para overlays:** `from-black/50 to-transparent`
- **Para botones:** `from-santa-marta-blue to-blue-600`
- **Para fondos:** Sutiles, usando colores de la paleta

#### 3. **Estados de Interacción**
- **Hover:** Elevación sutil, cambio de color
- **Focus:** Ring azul de 2px, siempre visible
- **Active:** Escala ligera (0.98) y cambio de color
- **Disabled:** Opacidad 50%, cursor not-allowed

## Configuración de Tailwind

```javascript
// tailwind.config.js
module.exports = {
  content: [
    "./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        'SM-blue': '#0072c0',
        'SM-yellow': '#f8c200',
        'SM-gray': '#c3c5c5',
        'SM-black': '#282828',
      },
      fontFamily: {
        'sans': ['Nunito', 'system-ui', 'sans-serif'],
        'nunito': ['Nunito', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        'fluid-xl': 'clamp(1.75rem, 4vw, 2.25rem)',
        'fluid-lg': 'clamp(1.5rem, 3vw, 1.875rem)',
        'fluid-base': 'clamp(1rem, 2vw, 1.125rem)',
        'fluid-sm': 'clamp(0.875rem, 1.5vw, 1rem)',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      borderRadius: {
        'xl': '12px',
        '2xl': '16px',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'pulse-gentle': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
}
```

## Componentes UI

### 1. Header de Navegación

```html
<header class="bg-white dark:bg-slate-900 shadow-md border-b border-slate-200 dark:border-slate-700">
  <nav class="container mx-auto px-4 py-3">
    <div class="flex justify-between items-center">
      <!-- Logo -->
      <div class="flex-shrink-0">
        <img src="/logos/logo-horizontal.svg" 
             class="h-10 block dark:hidden" 
             alt="Audioguías Murales Santa Marta">
        <img src="/logos/logo-white.svg" 
             class="h-10 hidden dark:block" 
             alt="Audioguías Murales Santa Marta">
      </div>
      
      <!-- Controles de accesibilidad -->
      <div class="flex items-center space-x-2">
        <!-- Tamaño de fuente -->
        <button class="p-2 rounded-lg bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors"
                aria-label="Aumentar tamaño de fuente">
          <span class="font-bold text-sm">A+</span>
        </button>
        
        <!-- Alto contraste -->
        <button class="p-2 rounded-lg bg-slate-100 dark:bg-slate-800 hover:bg-slate-200 dark:hover:bg-slate-700 transition-colors"
                aria-label="Activar alto contraste">
          <svg class="w-5 h-5" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
          </svg>
        </button>
        
        <!-- Idioma -->
        <button class="px-3 py-2 rounded-lg bg-SM-blue text-white hover:opacity-90 transition-colors font-medium">
          EN
        </button>
      </div>
    </div>
  </nav>
</header>
```

### 2. Card de Audioguía

```html
<div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 overflow-hidden hover:shadow-xl transition-all duration-300 group">
  <!-- Imagen de fondo con overlay -->
  <div class="relative h-48 bg-gradient-to-br from-SM-blue to-blue-600">
    <img src="/images/mural-thumb.jpg"
         alt="Vista previa del mural"
         class="w-full h-full object-cover opacity-80">
    <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

    <!-- Indicador de tipo -->
    <div class="absolute top-4 left-4">
      <span class="px-3 py-1 bg-SM-yellow text-SM-black text-sm font-medium rounded-full">
        Audioguía Fácil
      </span>
    </div>
  </div>
  
  <!-- Contenido -->
  <div class="p-6">
    <h3 class="font-semibold text-xl text-slate-900 dark:text-slate-100 mb-2">
      Audioguía Fácil
    </h3>
    <p class="text-slate-600 dark:text-slate-300 text-sm leading-relaxed mb-4">
      Audio adaptado para discapacidad intelectual con descripciones simples y claras.
    </p>
    
    <!-- Información adicional -->
    <div class="flex items-center justify-between text-sm text-slate-500 dark:text-slate-400">
      <span class="flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        15 min
      </span>
      <span class="flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
        </svg>
        3 murales
      </span>
    </div>
    
    <!-- Botón de acción -->
    <button class="w-full mt-4 bg-SM-blue hover:opacity-90 text-white font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-SM-blue focus:ring-offset-2 dark:focus:ring-offset-slate-800">
      Comenzar audioguía
    </button>
  </div>
</div>
```

### 3. Reproductor de Audio

```html
<div class="bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6">
  <!-- Información de la pista actual -->
  <div class="flex items-center mb-6">
    <img src="/images/mural-current.jpg" 
         alt="Mural actual" 
         class="w-16 h-16 rounded-lg object-cover mr-4">
    <div class="flex-1 min-w-0">
      <h4 class="font-semibold text-lg text-slate-900 dark:text-slate-100 truncate">
        La llegada del color
      </h4>
      <p class="text-slate-600 dark:text-slate-300 text-sm">
        Dr. Torres Villarroel, 1
      </p>
    </div>
  </div>
  
  <!-- Controles de reproducción -->
  <div class="flex items-center justify-center space-x-6 mb-6">
    <button class="p-2 rounded-full hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
            aria-label="Pista anterior">
      <svg class="w-6 h-6 text-slate-600 dark:text-slate-300" fill="none" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M12.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0019 16V8a1 1 0 00-1.6-.8l-5.334 4zM4.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0011 16V8a1 1 0 00-1.6-.8l-5.334 4z"/>
      </svg>
    </button>
    
    <button class="p-4 bg-SM-blue hover:opacity-90 text-white rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-SM-blue focus:ring-offset-2 dark:focus:ring-offset-slate-800"
            aria-label="Reproducir">
      <svg class="w-8 h-8" fill="none" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1l4-4v12l-4-4H9V10z"/>
      </svg>
    </button>
    
    <button class="p-2 rounded-full hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
            aria-label="Siguiente pista">
      <svg class="w-6 h-6 text-slate-600 dark:text-slate-300" fill="none" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="M11.933 12.8a1 1 0 000-1.6L6.6 7.2A1 1 0 005 8v8a1 1 0 001.6.8l5.333-4zM19.933 12.8a1 1 0 000-1.6l-5.333-4A1 1 0 0013 8v8a1 1 0 001.6.8l5.333-4z"/>
      </svg>
    </button>
  </div>
  
  <!-- Barra de progreso -->
  <div class="mb-4">
    <div class="flex justify-between text-xs text-slate-500 dark:text-slate-400 mb-1">
      <span>2:34</span>
      <span>5:47</span>
    </div>
    <div class="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2">
      <div class="bg-SM-blue h-2 rounded-full transition-all duration-300"
           style="width: 45%"></div>
    </div>
  </div>
  
  <!-- Control de volumen -->
  <div class="flex items-center space-x-3">
    <svg class="w-5 h-5 text-slate-600 dark:text-slate-300" fill="none" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M6.343 17.657l-.707.707"/>
    </svg>
    <div class="flex-1 bg-slate-200 dark:bg-slate-700 rounded-full h-2">
      <div class="bg-SM-yellow h-2 rounded-full" style="width: 70%"></div>
    </div>
  </div>
</div>
```

## Accesibilidad

### Principios WCAG 2.1 AA

#### 1. **Perceptible**
- Contraste mínimo 4.5:1 (texto normal) y 3:1 (texto grande)
- Texto alternativo para todas las imágenes
- Subtítulos y transcripciones para contenido de audio
- Información no dependiente únicamente del color

#### 2. **Operable**
- Navegación completa por teclado
- Sin contenido que cause convulsiones
- Tiempo suficiente para interactuar
- Skip links para navegación rápida

#### 3. **Comprensible**
- Idioma de página declarado
- Etiquetas descriptivas para formularios
- Mensajes de error claros
- Navegación consistente

#### 4. **Robusto**
- Código HTML válido
- Compatibilidad con tecnologías asistivas
- ARIA labels apropiados

### Implementación de Controles de Accesibilidad

```javascript
// Funcionalidad de alto contraste
function toggleHighContrast() {
  const html = document.documentElement;
  html.classList.toggle('high-contrast');
  localStorage.setItem('highContrast', html.classList.contains('high-contrast'));
}

// Control de tamaño de fuente
function adjustFontSize(direction) {
  const html = document.documentElement;
  const currentSize = parseInt(getComputedStyle(html).fontSize);
  const newSize = direction === 'increase' ? currentSize + 2 : currentSize - 2;
  const clampedSize = Math.max(14, Math.min(24, newSize));
  
  html.style.fontSize = `${clampedSize}px`;
  localStorage.setItem('fontSize', clampedSize);
}

// Reducción de movimiento
const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
if (prefersReducedMotion.matches) {
  document.documentElement.classList.add('reduce-motion');
}
```

### CSS para Accesibilidad

```css
/* Alto contraste */
.high-contrast {
  --tw-text-slate-900: #000000;
  --tw-text-slate-100: #ffffff;
  --tw-bg-white: #ffffff;
  --tw-bg-slate-900: #000000;
  --tw-border-slate-200: #000000;
}

.high-contrast button, .high-contrast .card {
  border: 2px solid currentColor !important;
}

/* Reducción de movimiento */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

/* Focus visible mejorado */
.focus-visible:focus {
  outline: 2px solid #0072c0;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(0, 114, 192, 0.1);
}

/* Skip links */
.skip-link {
  position: absolute;
  left: -9999px;
  top: 0;
  z-index: 999;
  padding: 8px 16px;
  background: #0072c0;
  color: white;
  text-decoration: none;
  font-weight: 600;
}

.skip-link:focus {
  left: 6px;
  top: 6px;
}
```

## Usos Incorrectos

Para mantener la integridad de la identidad de marca, se deben evitar los siguientes usos incorrectos:

### ❌ Errores de Color
1. **Usar colores fuera de la paleta establecida**
2. **Contrastar insuficiente entre texto y fondo**
3. **Depender únicamente del color para transmitir información**
4. **Mezclar paletas de otros proyectos**

### ❌ Errores Tipográficos
1. **Cambiar la fuente Nunito por otras opciones**
2. **Usar más de 3 pesos de fuente diferentes en una vista**
3. **Ignorar la jerarquía tipográfica establecida**
4. **Texto demasiado pequeño en móviles (menor a 16px)**

### ❌ Errores de Layout
1. **Cards con proporciones diferentes a las especificadas**
2. **Espaciado inconsistente entre elementos**
3. **Ignorar el sistema de grid responsive**
4. **Overlays que impidan la legibilidad**

### ❌ Errores de Accesibilidad
1. **Eliminar los controles de accesibilidad del header**
2. **Omitir textos alternativos en imágenes**
3. **Crear interacciones no navegables por teclado**
4. **Usar únicamente color para indicar estados**

### ❌ Errores de Marca
1. **Modificar el logotipo sin autorización**
2. **Usar el proyecto para fines comerciales no autorizados**
3. **Asociar la marca con contenido inapropiado**
4. **Omitir créditos al ayuntamiento de Santa Marta de Tormes**

---

Este manual ha sido desarrollado específicamente para el proyecto "Audioguías Murales Santa Marta" utilizando Astro 5, React 19 y Tailwind CSS 4, con máximo énfasis en accesibilidad universal y experiencia de usuario optimizada para dispositivos móviles.

**Versión:** 1.0  
**Fecha:** Enero 2025  
**Responsable:** Equipo de desarrollo audioguías  
**Próxima revisión:** Marzo 2025 