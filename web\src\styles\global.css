@import "tailwindcss";

/* Configuración de modo oscuro manual para Tailwind CSS 4 */
@custom-variant dark (&:where(.dark, .dark *));

/* Configuración de Tailwind CSS 4 */
@theme {
  /* Colores básicos de Tailwind */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-transparent: transparent;

  /* Escala de grises */
  --color-slate-50: #f8fafc;
  --color-slate-100: #f1f5f9;
  --color-slate-200: #e2e8f0;
  --color-slate-300: #cbd5e1;
  --color-slate-400: #94a3b8;
  --color-slate-500: #64748b;
  --color-slate-600: #475569;
  --color-slate-700: #334155;
  --color-slate-800: #1e293b;
  --color-slate-900: #0f172a;

  /* Colores azules */
  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-300: #93c5fd;
  --color-blue-400: #60a5fa;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  --color-blue-800: #1e40af;
  --color-blue-900: #1e3a8a;

  /* Colores amarillos */
  --color-yellow-50: #fefce8;
  --color-yellow-100: #fef3c7;
  --color-yellow-200: #fde68a;
  --color-yellow-300: #fcd34d;
  --color-yellow-400: #fbbf24;
  --color-yellow-500: #f59e0b;
  --color-yellow-600: #d97706;
  --color-yellow-700: #b45309;
  --color-yellow-800: #92400e;
  --color-yellow-900: #78350f;

  /* Colores grises */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Colores personalizados del manual de identidad */
  --color-SM-blue: #0072c0;
  --color-SM-yellow: #f8c200;
  --color-SM-gray: #c3c5c5;
  --color-SM-black: #282828;

  /* Fuentes */
  --font-family-nunito: "Nunito", system-ui, sans-serif;
  --font-family-sans: "Nunito", system-ui, sans-serif;

  /* Tamaños de fuente fluidos */
  --font-size-fluid-xl: clamp(1.75rem, 4vw, 2.25rem);
  --font-size-fluid-lg: clamp(1.5rem, 3vw, 1.875rem);
  --font-size-fluid-base: clamp(1rem, 2vw, 1.125rem);
  --font-size-fluid-sm: clamp(0.875rem, 1.5vw, 1rem);

  /* Espaciado personalizado */
  --spacing-18: 4.5rem;
  --spacing-88: 22rem;

  /* Border radius personalizado */
  --radius-xl: 12px;
  --radius-2xl: 16px;

  /* Animaciones */
  --animate-fade-in: fadeIn 0.5s ease-in-out;
  --animate-slide-up: slideUp 0.3s ease-out;
  --animate-pulse-gentle: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Fuentes Nunito */
@font-face {
  font-family: 'Nunito';
  src: url('/fonts/Nunito-VariableFont_wght.ttf') format('truetype-variations');
  font-weight: 200 1000;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nunito';
  src: url('/fonts/Nunito-Italic-VariableFont_wght.ttf') format('truetype-variations');
  font-weight: 200 1000;
  font-style: italic;
  font-display: swap;
}

/* Keyframes para animaciones */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(10px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

/* Configuración de fuentes responsivas fluidas */
.text-fluid-xl { font-size: clamp(1.75rem, 4vw, 2.25rem); }
.text-fluid-lg { font-size: clamp(1.5rem, 3vw, 1.875rem); }
.text-fluid-base { font-size: clamp(1rem, 2vw, 1.125rem); }
.text-fluid-sm { font-size: clamp(0.875rem, 1.5vw, 1rem); }

/* Clases de accesibilidad del manual de identidad */

/* Alto contraste */
.high-contrast {
  --tw-text-slate-900: #000000;
  --tw-text-slate-100: #ffffff;
  --tw-bg-white: #ffffff;
  --tw-bg-slate-900: #000000;
  --tw-border-slate-200: #000000;
}

.high-contrast button, .high-contrast .card {
  border: 2px solid currentColor !important;
}

/* Reducción de movimiento */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

/* Focus visible mejorado */
.focus-visible:focus {
  outline: 2px solid var(--color-SM-blue);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(0, 114, 192, 0.1);
}

/* Skip links */
.skip-link {
  position: absolute;
  left: -9999px;
  top: 0;
  z-index: 999;
  padding: 8px 16px;
  background: var(--color-SM-blue);
  color: white;
  text-decoration: none;
  font-weight: 600;
}

.skip-link:focus {
  left: 6px;
  top: 6px;
}

/* Estilos base para el proyecto */
* {
  box-sizing: border-box;
}

html {
  font-family: 'Nunito', system-ui, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

body {
  margin: 0;
  background-color: #f8fafc; /* slate-50 */
  color: #0f172a; /* slate-900 */
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Modo oscuro */
.dark body {
  background-color: #0f172a; /* slate-900 */
  color: #f1f5f9; /* slate-100 */
}

/* Iconos del modo oscuro */
.sun-icon {
  display: none;
}

.moon-icon {
  display: block;
}

.dark .sun-icon {
  display: block;
}

.dark .moon-icon {
  display: none;
}

/* Transiciones suaves para elementos interactivos */
button, 
.card,
header,
footer {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Mejor contraste para enlaces en modo oscuro */
.dark a {
  color: #60a5fa; /* blue-400 */
}

.dark a:hover {
  color: #93c5fd; /* blue-300 */
}

/* Estados de hover mejorados */
.dark .hover\:bg-slate-100:hover {
  background-color: #334155 !important; /* slate-700 */
}

.dark .hover\:bg-slate-200:hover {
  background-color: #475569 !important; /* slate-600 */
} 